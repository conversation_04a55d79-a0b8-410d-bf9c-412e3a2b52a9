﻿Imports System.Windows.Forms

Module Program
    ''' <summary>
    ''' Entry point utama aplikasi Toko/Penjualan
    ''' </summary>
    <STAThread()>
    Sub Main()
        ' Enable visual styles
        Application.EnableVisualStyles()
        Application.SetCompatibleTextRenderingDefault(False)

        ' Set culture untuk format mata uang Indonesia
        System.Threading.Thread.CurrentThread.CurrentCulture = New System.Globalization.CultureInfo("id-ID")
        System.Threading.Thread.CurrentThread.CurrentUICulture = New System.Globalization.CultureInfo("id-ID")

        ' Test database connection on startup
        If Not TestDatabaseConnection() Then
            MessageBox.Show("Tidak dapat terhubung ke database!" & vbCrLf &
                           "Pastikan MySQL server berjalan dan konfigurasi database benar.",
                           "Error Koneksi Database",
                           MessageBoxButtons.OK,
                           MessageBoxIcon.Error)
            Return
        End If

        ' Show splash screen (optional)
        ShowSplashScreen()

        ' Start the application with login form
        Application.Run(New FormLogin())
    End Sub

    ''' <summary>
    ''' Test koneksi database saat aplikasi dimulai
    ''' </summary>
    ''' <returns>True jika koneksi berhasil</returns>
    Private Function TestDatabaseConnection() As Boolean
        Try
            Return DatabaseHelper.TestConnection()
        Catch ex As Exception
            MessageBox.Show("Error testing database connection: " & ex.Message,
                           "Database Error",
                           MessageBoxButtons.OK,
                           MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ''' <summary>
    ''' Tampilkan splash screen saat aplikasi dimulai (optional)
    ''' </summary>
    Private Sub ShowSplashScreen()
        Try
            ' Create simple splash screen
            Dim splash As New Form()
            splash.Text = ""
            splash.Size = New Size(400, 300)
            splash.StartPosition = FormStartPosition.CenterScreen
            splash.FormBorderStyle = FormBorderStyle.None
            splash.BackColor = Color.FromArgb(52, 73, 94)

            ' Add logo/title
            Dim lblTitle As New Label()
            lblTitle.Text = "APLIKASI TOKO/PENJUALAN"
            lblTitle.Font = New Font("Segoe UI", 18, FontStyle.Bold)
            lblTitle.ForeColor = Color.White
            lblTitle.Size = New Size(400, 40)
            lblTitle.Location = New Point(0, 80)
            lblTitle.TextAlign = ContentAlignment.MiddleCenter
            splash.Controls.Add(lblTitle)

            Dim lblSubtitle As New Label()
            lblSubtitle.Text = "Sistem Manajemen Toko Modern"
            lblSubtitle.Font = New Font("Segoe UI", 12)
            lblSubtitle.ForeColor = Color.White
            lblSubtitle.Size = New Size(400, 30)
            lblSubtitle.Location = New Point(0, 120)
            lblSubtitle.TextAlign = ContentAlignment.MiddleCenter
            splash.Controls.Add(lblSubtitle)

            Dim lblVersion As New Label()
            lblVersion.Text = "Version 1.0"
            lblVersion.Font = New Font("Segoe UI", 10)
            lblVersion.ForeColor = Color.FromArgb(189, 195, 199)
            lblVersion.Size = New Size(400, 25)
            lblVersion.Location = New Point(0, 160)
            lblVersion.TextAlign = ContentAlignment.MiddleCenter
            splash.Controls.Add(lblVersion)

            Dim lblLoading As New Label()
            lblLoading.Text = "Memuat aplikasi..."
            lblLoading.Font = New Font("Segoe UI", 10)
            lblLoading.ForeColor = Color.FromArgb(189, 195, 199)
            lblLoading.Size = New Size(400, 25)
            lblLoading.Location = New Point(0, 220)
            lblLoading.TextAlign = ContentAlignment.MiddleCenter
            splash.Controls.Add(lblLoading)

            ' Progress bar
            Dim progressBar As New ProgressBar()
            progressBar.Size = New Size(300, 20)
            progressBar.Location = New Point(50, 250)
            progressBar.Style = ProgressBarStyle.Marquee
            progressBar.MarqueeAnimationSpeed = 30
            splash.Controls.Add(progressBar)

            ' Show splash for 2 seconds
            splash.Show()
            Application.DoEvents()
            System.Threading.Thread.Sleep(2000)
            splash.Close()

        Catch ex As Exception
            ' If splash screen fails, continue without it
        End Try
    End Sub
End Module

''' <summary>
''' Application settings dan konfigurasi
''' </summary>
Public Class AppSettings
    Public Shared ReadOnly Property ApplicationName As String = "Aplikasi Toko/Penjualan"
    Public Shared ReadOnly Property Version As String = "1.0.0"
    Public Shared ReadOnly Property CompanyName As String = "Sistem Manajemen Toko"
    Public Shared ReadOnly Property Copyright As String = "© 2024 - Sistem Manajemen Toko Modern"

    ' Database settings (bisa dipindah ke config file)
    Public Shared ReadOnly Property DatabaseServer As String = "127.0.0.1"
    Public Shared ReadOnly Property DatabaseName As String = "toko_penjualan"
    Public Shared ReadOnly Property DatabaseUser As String = "root"

    ' UI Settings
    Public Shared ReadOnly Property PrimaryColor As Color = Color.FromArgb(52, 73, 94)
    Public Shared ReadOnly Property SecondaryColor As Color = Color.FromArgb(52, 152, 219)
    Public Shared ReadOnly Property SuccessColor As Color = Color.FromArgb(46, 204, 113)
    Public Shared ReadOnly Property WarningColor As Color = Color.FromArgb(243, 156, 18)
    Public Shared ReadOnly Property DangerColor As Color = Color.FromArgb(231, 76, 60)
    Public Shared ReadOnly Property InfoColor As Color = Color.FromArgb(155, 89, 182)
    Public Shared ReadOnly Property LightColor As Color = Color.FromArgb(236, 240, 241)
    Public Shared ReadOnly Property DarkColor As Color = Color.FromArgb(52, 73, 94)

    ' Font settings
    Public Shared ReadOnly Property DefaultFont As Font = New Font("Segoe UI", 10)
    Public Shared ReadOnly Property HeaderFont As Font = New Font("Segoe UI", 14, FontStyle.Bold)
    Public Shared ReadOnly Property ButtonFont As Font = New Font("Segoe UI", 10, FontStyle.Bold)
End Class

''' <summary>
''' Utility class untuk fungsi-fungsi umum aplikasi
''' </summary>
Public Class AppUtility
    ''' <summary>
    ''' Format mata uang Indonesia
    ''' </summary>
    Public Shared Function FormatCurrency(amount As Decimal) As String
        Return amount.ToString("C0", New System.Globalization.CultureInfo("id-ID"))
    End Function

    ''' <summary>
    ''' Format tanggal Indonesia
    ''' </summary>
    Public Shared Function FormatDate(dateValue As DateTime) As String
        Return dateValue.ToString("dd/MM/yyyy")
    End Function

    ''' <summary>
    ''' Format tanggal dan waktu Indonesia
    ''' </summary>
    Public Shared Function FormatDateTime(dateValue As DateTime) As String
        Return dateValue.ToString("dd/MM/yyyy HH:mm:ss")
    End Function

    ''' <summary>
    ''' Validasi email format
    ''' </summary>
    Public Shared Function IsValidEmail(email As String) As Boolean
        Try
            Dim addr As New System.Net.Mail.MailAddress(email)
            Return addr.Address = email
        Catch
            Return False
        End Try
    End Function

    ''' <summary>
    ''' Generate kode produk otomatis
    ''' </summary>
    Public Shared Function GenerateProductCode(kategoriId As Integer) As String
        Try
            Dim kategoriRepo As New KategoriRepository()
            Dim kategori As Kategori = kategoriRepo.GetKategoriById(kategoriId)

            If kategori IsNot Nothing AndAlso Not String.IsNullOrEmpty(kategori.NamaKategori) Then
                Dim prefix As String = kategori.NamaKategori.Substring(0, Math.Min(3, kategori.NamaKategori.Length)).ToUpper()
                Dim timestamp As String = DateTime.Now.ToString("yyyyMMddHHmmss")
                Return $"{prefix}{timestamp}"
            Else
                Return $"PRD{DateTime.Now:yyyyMMddHHmmss}"
            End If
        Catch
            Return $"PRD{DateTime.Now:yyyyMMddHHmmss}"
        End Try
    End Function

    ''' <summary>
    ''' Log error ke file (untuk debugging)
    ''' </summary>
    Public Shared Sub LogError(ex As Exception, Optional context As String = "")
        Try
            Dim logPath As String = Path.Combine(Application.StartupPath, "Logs")
            If Not Directory.Exists(logPath) Then
                Directory.CreateDirectory(logPath)
            End If

            Dim logFile As String = Path.Combine(logPath, $"error_{DateTime.Now:yyyyMMdd}.log")
            Dim logEntry As String = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {context} - {ex.Message}{vbCrLf}{ex.StackTrace}{vbCrLf}{vbCrLf}"

            File.AppendAllText(logFile, logEntry)
        Catch
            ' Ignore logging errors
        End Try
    End Sub

    ''' <summary>
    ''' Show confirmation dialog dengan styling konsisten
    ''' </summary>
    Public Shared Function ShowConfirmation(message As String, Optional title As String = "Konfirmasi") As Boolean
        Return MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes
    End Function

    ''' <summary>
    ''' Show success message dengan styling konsisten
    ''' </summary>
    Public Shared Sub ShowSuccess(message As String, Optional title As String = "Sukses")
        MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    ''' <summary>
    ''' Show error message dengan styling konsisten
    ''' </summary>
    Public Shared Sub ShowError(message As String, Optional title As String = "Error")
        MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Error)
    End Sub

    ''' <summary>
    ''' Show warning message dengan styling konsisten
    ''' </summary>
    Public Shared Sub ShowWarning(message As String, Optional title As String = "Peringatan")
        MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning)
    End Sub
End Class
