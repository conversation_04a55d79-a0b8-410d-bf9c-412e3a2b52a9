Imports System.Windows.Forms
Imports System.Drawing

Public Class TestForm
    Inherits Form

    Public Sub New()
        InitializeComponent()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "Test Login - TokoPenjual"
        Me.Size = New Size(400, 200)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False

        ' Create test button
        Dim btnTest As New Button()
        btnTest.Text = "Test Login & Open Main Form"
        btnTest.Size = New Size(250, 40)
        btnTest.Location = New Point(75, 50)
        btnTest.BackColor = Color.FromArgb(52, 152, 219)
        btnTest.ForeColor = Color.White
        btnTest.FlatStyle = FlatStyle.Flat
        btnTest.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        AddHandler btnTest.Click, AddressOf BtnTest_Click

        ' Create status label
        Dim lblStatus As New Label()
        lblStatus.Name = "lblStatus"
        lblStatus.Text = "Click button to test login"
        lblStatus.Size = New Size(350, 30)
        lblStatus.Location = New Point(25, 110)
        lblStatus.TextAlign = ContentAlignment.MiddleCenter
        lblStatus.Font = New Font("Segoe UI", 9)

        Me.Controls.Add(btnTest)
        Me.Controls.Add(lblStatus)
    End Sub

    Private Sub BtnTest_Click(sender As Object, e As EventArgs)
        Dim lblStatus As Label = DirectCast(Me.Controls("lblStatus"), Label)
        
        Try
            lblStatus.Text = "Creating test user..."
            lblStatus.ForeColor = Color.Blue
            Application.DoEvents()

            ' Create test user
            Dim testUser As New User()
            testUser.IdUser = 999
            testUser.Username = "testuser"
            testUser.NamaLengkap = "Test User"
            testUser.Role = "admin"
            testUser.Email = "<EMAIL>"
            testUser.StatusAktif = True

            lblStatus.Text = "Setting CurrentUser..."
            Application.DoEvents()

            ' Set current user
            CurrentUser.Instance.SetUser(testUser)

            lblStatus.Text = "Verifying CurrentUser..."
            Application.DoEvents()

            ' Debug info
            System.Diagnostics.Debug.WriteLine($"Test - CurrentUser ID: {CurrentUser.Instance.IdUser}")
            System.Diagnostics.Debug.WriteLine($"Test - CurrentUser Username: {CurrentUser.Instance.Username}")
            System.Diagnostics.Debug.WriteLine($"Test - CurrentUser Role: {CurrentUser.Instance.Role}")
            System.Diagnostics.Debug.WriteLine($"Test - IsLoggedIn: {CurrentUser.Instance.IsLoggedIn()}")

            ' Check if login is valid
            If CurrentUser.Instance.IsLoggedIn() Then
                lblStatus.Text = "Login valid! Opening main form..."
                lblStatus.ForeColor = Color.Green
                Application.DoEvents()
                
                System.Threading.Thread.Sleep(1000)

                ' Hide this form and show main form
                Me.Hide()
                Dim mainForm As New FormUtama()
                mainForm.Show()
                AddHandler mainForm.FormClosed, Sub() Me.Close()
            Else
                lblStatus.Text = $"Login FAILED! ID={CurrentUser.Instance.IdUser}, User={CurrentUser.Instance.Username}"
                lblStatus.ForeColor = Color.Red
            End If

        Catch ex As Exception
            lblStatus.Text = "Error: " & ex.Message
            lblStatus.ForeColor = Color.Red
            System.Diagnostics.Debug.WriteLine("Test Error: " & ex.ToString())
        End Try
    End Sub
End Class
