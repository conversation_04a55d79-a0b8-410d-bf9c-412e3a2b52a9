﻿Public Class FormLogin
    Private userRepo As New UserRepository()

    Private Sub FormLogin_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        InitializeForm()
        SetupModernDesign()
    End Sub

    Private Sub InitializeForm()
        ' Set form properties
        Me.Text = "Login - Aplikasi Toko"
        Me.Size = New Size(400, 500)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.None
        Me.BackColor = Color.FromArgb(240, 240, 240)

        ' Disable maximize and minimize
        Me.MaximizeBox = False
        Me.MinimizeBox = False

        ' Clear any existing controls
        Me.Controls.Clear()

        CreateControls()
    End Sub

    Private Sub CreateControls()
        ' Main Panel
        Dim mainPanel As New Panel()
        mainPanel.Size = New Size(350, 450)
        mainPanel.Location = New Point(25, 25)
        mainPanel.BackColor = Color.White
        mainPanel.BorderStyle = BorderStyle.None
        Me.Controls.Add(mainPanel)

        ' Header Panel
        Dim headerPanel As New Panel()
        headerPanel.Size = New Size(350, 80)
        headerPanel.Location = New Point(0, 0)
        headerPanel.BackColor = Color.FromArgb(52, 152, 219)
        mainPanel.Controls.Add(headerPanel)

        ' Title Label
        Dim lblTitle As New Label()
        lblTitle.Text = "LOGIN SISTEM"
        lblTitle.Font = New Font("Segoe UI", 16, FontStyle.Bold)
        lblTitle.ForeColor = Color.White
        lblTitle.Size = New Size(350, 30)
        lblTitle.Location = New Point(0, 15)
        lblTitle.TextAlign = ContentAlignment.MiddleCenter
        headerPanel.Controls.Add(lblTitle)

        ' Subtitle Label
        Dim lblSubtitle As New Label()
        lblSubtitle.Text = "Aplikasi Toko/Penjualan"
        lblSubtitle.Font = New Font("Segoe UI", 10)
        lblSubtitle.ForeColor = Color.White
        lblSubtitle.Size = New Size(350, 20)
        lblSubtitle.Location = New Point(0, 45)
        lblSubtitle.TextAlign = ContentAlignment.MiddleCenter
        headerPanel.Controls.Add(lblSubtitle)

        ' Username Label
        Dim lblUsername As New Label()
        lblUsername.Text = "Username:"
        lblUsername.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblUsername.ForeColor = Color.FromArgb(52, 73, 94)
        lblUsername.Size = New Size(100, 25)
        lblUsername.Location = New Point(30, 120)
        mainPanel.Controls.Add(lblUsername)

        ' Username TextBox
        Dim txtUsername As New TextBox()
        txtUsername.Name = "txtUsername"
        txtUsername.Font = New Font("Segoe UI", 11)
        txtUsername.Size = New Size(290, 30)
        txtUsername.Location = New Point(30, 145)
        txtUsername.BorderStyle = BorderStyle.FixedSingle
        mainPanel.Controls.Add(txtUsername)

        ' Password Label
        Dim lblPassword As New Label()
        lblPassword.Text = "Password:"
        lblPassword.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblPassword.ForeColor = Color.FromArgb(52, 73, 94)
        lblPassword.Size = New Size(100, 25)
        lblPassword.Location = New Point(30, 190)
        mainPanel.Controls.Add(lblPassword)

        ' Password TextBox
        Dim txtPassword As New TextBox()
        txtPassword.Name = "txtPassword"
        txtPassword.Font = New Font("Segoe UI", 11)
        txtPassword.Size = New Size(290, 30)
        txtPassword.Location = New Point(30, 215)
        txtPassword.BorderStyle = BorderStyle.FixedSingle
        txtPassword.UseSystemPasswordChar = True
        mainPanel.Controls.Add(txtPassword)

        ' Show Password CheckBox
        Dim chkShowPassword As New CheckBox()
        chkShowPassword.Name = "chkShowPassword"
        chkShowPassword.Text = "Tampilkan Password"
        chkShowPassword.Font = New Font("Segoe UI", 9)
        chkShowPassword.ForeColor = Color.FromArgb(52, 73, 94)
        chkShowPassword.Size = New Size(150, 20)
        chkShowPassword.Location = New Point(30, 250)
        AddHandler chkShowPassword.CheckedChanged, AddressOf ChkShowPassword_CheckedChanged
        mainPanel.Controls.Add(chkShowPassword)

        ' Login Button
        Dim btnLogin As New Button()
        btnLogin.Name = "btnLogin"
        btnLogin.Text = "LOGIN"
        btnLogin.Font = New Font("Segoe UI", 11, FontStyle.Bold)
        btnLogin.Size = New Size(290, 40)
        btnLogin.Location = New Point(30, 290)
        btnLogin.BackColor = Color.FromArgb(46, 204, 113)
        btnLogin.ForeColor = Color.White
        btnLogin.FlatStyle = FlatStyle.Flat
        btnLogin.FlatAppearance.BorderSize = 0
        btnLogin.Cursor = Cursors.Hand
        AddHandler btnLogin.Click, AddressOf BtnLogin_Click
        mainPanel.Controls.Add(btnLogin)

        ' Cancel Button
        Dim btnCancel As New Button()
        btnCancel.Name = "btnCancel"
        btnCancel.Text = "BATAL"
        btnCancel.Font = New Font("Segoe UI", 11, FontStyle.Bold)
        btnCancel.Size = New Size(290, 40)
        btnCancel.Location = New Point(30, 340)
        btnCancel.BackColor = Color.FromArgb(231, 76, 60)
        btnCancel.ForeColor = Color.White
        btnCancel.FlatStyle = FlatStyle.Flat
        btnCancel.FlatAppearance.BorderSize = 0
        btnCancel.Cursor = Cursors.Hand
        AddHandler btnCancel.Click, AddressOf BtnCancel_Click
        mainPanel.Controls.Add(btnCancel)

        ' Status Label
        Dim lblStatus As New Label()
        lblStatus.Name = "lblStatus"
        lblStatus.Text = ""
        lblStatus.Font = New Font("Segoe UI", 9)
        lblStatus.ForeColor = Color.FromArgb(231, 76, 60)
        lblStatus.Size = New Size(290, 20)
        lblStatus.Location = New Point(30, 390)
        lblStatus.TextAlign = ContentAlignment.MiddleCenter
        mainPanel.Controls.Add(lblStatus)

        ' Set default button and focus
        Me.AcceptButton = btnLogin
        txtUsername.Focus()
    End Sub

    Private Sub SetupModernDesign()
        ' Add shadow effect to main panel
        Dim mainPanel As Panel = DirectCast(Me.Controls(0), Panel)

        ' Create rounded corners effect (simple approach)
        Dim path As New System.Drawing.Drawing2D.GraphicsPath()
        Dim radius As Integer = 10
        path.AddArc(0, 0, radius, radius, 180, 90)
        path.AddArc(mainPanel.Width - radius, 0, radius, radius, 270, 90)
        path.AddArc(mainPanel.Width - radius, mainPanel.Height - radius, radius, radius, 0, 90)
        path.AddArc(0, mainPanel.Height - radius, radius, radius, 90, 90)
        path.CloseAllFigures()
        mainPanel.Region = New Region(path)
    End Sub

    Private Sub ChkShowPassword_CheckedChanged(sender As Object, e As EventArgs)
        Dim chkShowPassword As CheckBox = DirectCast(sender, CheckBox)
        Dim txtPassword As TextBox = DirectCast(Me.Controls(0).Controls("txtPassword"), TextBox)
        txtPassword.UseSystemPasswordChar = Not chkShowPassword.Checked
    End Sub

    Private Sub BtnLogin_Click(sender As Object, e As EventArgs)
        Dim txtUsername As TextBox = DirectCast(Me.Controls(0).Controls("txtUsername"), TextBox)
        Dim txtPassword As TextBox = DirectCast(Me.Controls(0).Controls("txtPassword"), TextBox)
        Dim lblStatus As Label = DirectCast(Me.Controls(0).Controls("lblStatus"), Label)

        ' Clear previous status
        lblStatus.Text = ""
        lblStatus.ForeColor = Color.FromArgb(231, 76, 60)

        ' Validate input
        If String.IsNullOrWhiteSpace(txtUsername.Text) Then
            lblStatus.Text = "Username tidak boleh kosong!"
            txtUsername.Focus()
            Return
        End If

        If String.IsNullOrWhiteSpace(txtPassword.Text) Then
            lblStatus.Text = "Password tidak boleh kosong!"
            txtPassword.Focus()
            Return
        End If

        ' Disable login button during process
        Dim btnLogin As Button = DirectCast(sender, Button)
        btnLogin.Enabled = False
        btnLogin.Text = "MEMPROSES..."

        Try
            ' Quick test bypass - remove this after database is set up
            If txtUsername.Text.Trim().ToLower() = "test" AndAlso txtPassword.Text = "test" Then
                System.Diagnostics.Debug.WriteLine("Test bypass triggered")

                ' Create a test user for demonstration
                Dim testUser As New User()
                testUser.IdUser = 999
                testUser.Username = "testuser"
                testUser.NamaLengkap = "Test User"
                testUser.Role = "admin"
                testUser.Email = "<EMAIL>"
                testUser.StatusAktif = True

                System.Diagnostics.Debug.WriteLine($"Test user created - ID: {testUser.IdUser}, Username: {testUser.Username}")

                CurrentUser.Instance.SetUser(testUser)

                ' Debug: Check if CurrentUser was set properly
                System.Diagnostics.Debug.WriteLine($"After SetUser - ID: {CurrentUser.Instance.IdUser}, Username: {CurrentUser.Instance.Username}, IsLoggedIn: {CurrentUser.Instance.IsLoggedIn()}")

                ' Verify again before showing main form
                If Not CurrentUser.Instance.IsLoggedIn() Then
                    lblStatus.Text = $"Test login failed - ID: {CurrentUser.Instance.IdUser}, User: {CurrentUser.Instance.Username}"
                    btnLogin.Enabled = True
                    btnLogin.Text = "LOGIN"
                    Return
                End If

                lblStatus.ForeColor = Color.FromArgb(46, 204, 113)
                lblStatus.Text = "Login berhasil! (Mode Test)"
                System.Threading.Thread.Sleep(1000)

                System.Diagnostics.Debug.WriteLine("About to hide login form and show main form")
                Me.Hide()
                Dim mainForm As New FormUtama()
                mainForm.Show()
                AddHandler mainForm.FormClosed, Sub() Me.Close()
                Return
            End If

            ' Test database connection first
            If Not DatabaseHelper.TestConnection() Then
                lblStatus.Text = "Koneksi database gagal! Gunakan test/test untuk mode demo."
                Return
            End If

            ' Attempt login
            Dim userRepo As New UserRepository()
            Dim result As DatabaseResponse = userRepo.Login(txtUsername.Text, txtPassword.Text)

            If result.Success Then
                lblStatus.ForeColor = Color.FromArgb(46, 204, 113)
                lblStatus.Text = "Login berhasil! Membuka aplikasi..."

                ' Small delay to show success message
                System.Threading.Thread.Sleep(1000)

                ' Verify that CurrentUser is properly set
                If Not CurrentUser.Instance.IsLoggedIn() Then
                    lblStatus.Text = $"Error: Gagal mengatur sesi user (ID: {CurrentUser.Instance.IdUser}, User: {CurrentUser.Instance.Username})"
                    Return
                End If

                ' Hide login form and show main form
                Me.Hide()

                ' Open main form
                Dim mainForm As New FormUtama()
                mainForm.Show()

                ' Close login form when main form is closed
                AddHandler mainForm.FormClosed, Sub() Me.Close()

            Else
                lblStatus.Text = result.Message
                txtPassword.Clear()
                txtUsername.Focus()
                ' Re-enable login button on failed login
                btnLogin.Enabled = True
                btnLogin.Text = "LOGIN"
            End If

        Catch ex As Exception
            lblStatus.Text = "Error: " & ex.Message
            ' Re-enable login button on error
            btnLogin.Enabled = True
            btnLogin.Text = "LOGIN"
        End Try
    End Sub

    Private Sub BtnCancel_Click(sender As Object, e As EventArgs)
        If MessageBox.Show("Apakah Anda yakin ingin keluar?", "Konfirmasi", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Application.Exit()
        End If
    End Sub

    Private Sub FormLogin_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.KeyCode = Keys.Escape Then
            BtnCancel_Click(Nothing, Nothing)
        End If
    End Sub

    ' Handle form dragging (since FormBorderStyle is None)
    Private isDragging As Boolean = False
    Private dragOffset As Point

    Private Sub FormLogin_MouseDown(sender As Object, e As MouseEventArgs) Handles MyBase.MouseDown
        If e.Button = MouseButtons.Left Then
            isDragging = True
            dragOffset = New Point(e.X, e.Y)
        End If
    End Sub

    Private Sub FormLogin_MouseMove(sender As Object, e As MouseEventArgs) Handles MyBase.MouseMove
        If isDragging Then
            Me.Location = New Point(Me.Location.X + e.X - dragOffset.X, Me.Location.Y + e.Y - dragOffset.Y)
        End If
    End Sub

    Private Sub FormLogin_MouseUp(sender As Object, e As MouseEventArgs) Handles MyBase.MouseUp
        isDragging = False
    End Sub
End Class
