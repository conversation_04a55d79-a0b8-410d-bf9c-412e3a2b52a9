﻿Public Class FormProduk
    Private produkRepo As New ProdukRepository()
    Private kategoriRepo As New KategoriRepository()
    Private isEditMode As Boolean = False
    Private selectedProdukId As Integer = 0

    Private Sub FormProduk_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        InitializeForm()
        SetupDataGridView()
        LoadKategoriComboBox()
        LoadProdukData()
        SetFormMode(False)
    End Sub

    Private Sub InitializeForm()
        ' Set form properties
        Me.Text = "Manajemen Produk"
        Me.Size = New Size(1200, 700)
        Me.StartPosition = FormStartPosition.CenterParent
        Me.BackColor = Color.FromArgb(236, 240, 241)
        Me.MinimumSize = New Size(1000, 600)
        Me.WindowState = FormWindowState.Maximized

        CreateControls()
    End Sub

    Private Sub CreateControls()
        ' Main Panel
        Dim mainPanel As New TableLayoutPanel()
        mainPanel.Dock = DockStyle.Fill
        mainPanel.ColumnCount = 2
        mainPanel.RowCount = 1
        mainPanel.ColumnStyles.Add(New ColumnStyle(SizeType.Percent, 35))
        mainPanel.ColumnStyles.Add(New ColumnStyle(SizeType.Percent, 65))
        mainPanel.Padding = New Padding(10)
        Me.Controls.Add(mainPanel)

        ' Left Panel - Form Input
        Dim leftPanel As New Panel()
        leftPanel.BackColor = Color.White
        leftPanel.BorderStyle = BorderStyle.FixedSingle
        leftPanel.Padding = New Padding(15)
        leftPanel.AutoScroll = True
        mainPanel.Controls.Add(leftPanel, 0, 0)

        ' Right Panel - Data Grid
        Dim rightPanel As New Panel()
        rightPanel.BackColor = Color.White
        rightPanel.BorderStyle = BorderStyle.FixedSingle
        rightPanel.Padding = New Padding(15)
        mainPanel.Controls.Add(rightPanel, 1, 0)

        ' Create Left Panel Controls
        CreateInputControls(leftPanel)

        ' Create Right Panel Controls
        CreateDataGridControls(rightPanel)
    End Sub

    Private Sub CreateInputControls(parent As Panel)
        Dim yPos As Integer = 10

        ' Title
        Dim lblTitle As New Label()
        lblTitle.Text = "FORM PRODUK"
        lblTitle.Font = New Font("Segoe UI", 14, FontStyle.Bold)
        lblTitle.ForeColor = Color.FromArgb(52, 73, 94)
        lblTitle.Size = New Size(300, 30)
        lblTitle.Location = New Point(10, yPos)
        parent.Controls.Add(lblTitle)
        yPos += 50

        ' Kode Produk
        Dim lblKode As New Label()
        lblKode.Text = "Kode Produk:"
        lblKode.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblKode.ForeColor = Color.FromArgb(52, 73, 94)
        lblKode.Size = New Size(120, 25)
        lblKode.Location = New Point(10, yPos)
        parent.Controls.Add(lblKode)
        yPos += 25

        Dim txtKode As New TextBox()
        txtKode.Name = "txtKodeProduk"
        txtKode.Font = New Font("Segoe UI", 10)
        txtKode.Size = New Size(280, 25)
        txtKode.Location = New Point(10, yPos)
        txtKode.BorderStyle = BorderStyle.FixedSingle
        txtKode.CharacterCasing = CharacterCasing.Upper
        parent.Controls.Add(txtKode)
        yPos += 35

        ' Nama Produk
        Dim lblNama As New Label()
        lblNama.Text = "Nama Produk:"
        lblNama.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblNama.ForeColor = Color.FromArgb(52, 73, 94)
        lblNama.Size = New Size(120, 25)
        lblNama.Location = New Point(10, yPos)
        parent.Controls.Add(lblNama)
        yPos += 25

        Dim txtNama As New TextBox()
        txtNama.Name = "txtNamaProduk"
        txtNama.Font = New Font("Segoe UI", 10)
        txtNama.Size = New Size(280, 25)
        txtNama.Location = New Point(10, yPos)
        txtNama.BorderStyle = BorderStyle.FixedSingle
        parent.Controls.Add(txtNama)
        yPos += 35

        ' Kategori
        Dim lblKategori As New Label()
        lblKategori.Text = "Kategori:"
        lblKategori.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblKategori.ForeColor = Color.FromArgb(52, 73, 94)
        lblKategori.Size = New Size(120, 25)
        lblKategori.Location = New Point(10, yPos)
        parent.Controls.Add(lblKategori)
        yPos += 25

        Dim cmbKategori As New ComboBox()
        cmbKategori.Name = "cmbKategori"
        cmbKategori.Font = New Font("Segoe UI", 10)
        cmbKategori.Size = New Size(280, 25)
        cmbKategori.Location = New Point(10, yPos)
        cmbKategori.DropDownStyle = ComboBoxStyle.DropDownList
        parent.Controls.Add(cmbKategori)
        yPos += 35

        ' Harga Beli
        Dim lblHargaBeli As New Label()
        lblHargaBeli.Text = "Harga Beli:"
        lblHargaBeli.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblHargaBeli.ForeColor = Color.FromArgb(52, 73, 94)
        lblHargaBeli.Size = New Size(120, 25)
        lblHargaBeli.Location = New Point(10, yPos)
        parent.Controls.Add(lblHargaBeli)
        yPos += 25

        Dim txtHargaBeli As New TextBox()
        txtHargaBeli.Name = "txtHargaBeli"
        txtHargaBeli.Font = New Font("Segoe UI", 10)
        txtHargaBeli.Size = New Size(280, 25)
        txtHargaBeli.Location = New Point(10, yPos)
        txtHargaBeli.BorderStyle = BorderStyle.FixedSingle
        txtHargaBeli.TextAlign = HorizontalAlignment.Right
        AddHandler txtHargaBeli.KeyPress, AddressOf NumericTextBox_KeyPress
        AddHandler txtHargaBeli.Leave, AddressOf FormatCurrency_Leave
        parent.Controls.Add(txtHargaBeli)
        yPos += 35

        ' Harga Jual
        Dim lblHargaJual As New Label()
        lblHargaJual.Text = "Harga Jual:"
        lblHargaJual.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblHargaJual.ForeColor = Color.FromArgb(52, 73, 94)
        lblHargaJual.Size = New Size(120, 25)
        lblHargaJual.Location = New Point(10, yPos)
        parent.Controls.Add(lblHargaJual)
        yPos += 25

        Dim txtHargaJual As New TextBox()
        txtHargaJual.Name = "txtHargaJual"
        txtHargaJual.Font = New Font("Segoe UI", 10)
        txtHargaJual.Size = New Size(280, 25)
        txtHargaJual.Location = New Point(10, yPos)
        txtHargaJual.BorderStyle = BorderStyle.FixedSingle
        txtHargaJual.TextAlign = HorizontalAlignment.Right
        AddHandler txtHargaJual.KeyPress, AddressOf NumericTextBox_KeyPress
        AddHandler txtHargaJual.Leave, AddressOf FormatCurrency_Leave
        parent.Controls.Add(txtHargaJual)
        yPos += 35

        ' Stok
        Dim lblStok As New Label()
        lblStok.Text = "Stok:"
        lblStok.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblStok.ForeColor = Color.FromArgb(52, 73, 94)
        lblStok.Size = New Size(120, 25)
        lblStok.Location = New Point(10, yPos)
        parent.Controls.Add(lblStok)
        yPos += 25

        Dim txtStok As New NumericUpDown()
        txtStok.Name = "txtStok"
        txtStok.Font = New Font("Segoe UI", 10)
        txtStok.Size = New Size(130, 25)
        txtStok.Location = New Point(10, yPos)
        txtStok.Minimum = 0
        txtStok.Maximum = 999999
        txtStok.TextAlign = HorizontalAlignment.Right
        parent.Controls.Add(txtStok)

        ' Stok Minimum
        Dim lblStokMin As New Label()
        lblStokMin.Text = "Min:"
        lblStokMin.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblStokMin.ForeColor = Color.FromArgb(52, 73, 94)
        lblStokMin.Size = New Size(40, 25)
        lblStokMin.Location = New Point(150, yPos)
        parent.Controls.Add(lblStokMin)

        Dim txtStokMin As New NumericUpDown()
        txtStokMin.Name = "txtStokMinimum"
        txtStokMin.Font = New Font("Segoe UI", 10)
        txtStokMin.Size = New Size(100, 25)
        txtStokMin.Location = New Point(190, yPos)
        txtStokMin.Minimum = 0
        txtStokMin.Maximum = 9999
        txtStokMin.Value = 5
        txtStokMin.TextAlign = HorizontalAlignment.Right
        parent.Controls.Add(txtStokMin)
        yPos += 35

        ' Deskripsi
        Dim lblDeskripsi As New Label()
        lblDeskripsi.Text = "Deskripsi:"
        lblDeskripsi.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblDeskripsi.ForeColor = Color.FromArgb(52, 73, 94)
        lblDeskripsi.Size = New Size(120, 25)
        lblDeskripsi.Location = New Point(10, yPos)
        parent.Controls.Add(lblDeskripsi)
        yPos += 25

        Dim txtDeskripsi As New TextBox()
        txtDeskripsi.Name = "txtDeskripsi"
        txtDeskripsi.Font = New Font("Segoe UI", 10)
        txtDeskripsi.Size = New Size(280, 60)
        txtDeskripsi.Location = New Point(10, yPos)
        txtDeskripsi.BorderStyle = BorderStyle.FixedSingle
        txtDeskripsi.Multiline = True
        txtDeskripsi.ScrollBars = ScrollBars.Vertical
        parent.Controls.Add(txtDeskripsi)
        yPos += 80

        ' Profit Info Panel
        Dim profitPanel As New Panel()
        profitPanel.Size = New Size(280, 60)
        profitPanel.Location = New Point(10, yPos)
        profitPanel.BackColor = Color.FromArgb(236, 240, 241)
        profitPanel.BorderStyle = BorderStyle.FixedSingle
        parent.Controls.Add(profitPanel)

        Dim lblProfitTitle As New Label()
        lblProfitTitle.Text = "Informasi Profit:"
        lblProfitTitle.Font = New Font("Segoe UI", 9, FontStyle.Bold)
        lblProfitTitle.ForeColor = Color.FromArgb(52, 73, 94)
        lblProfitTitle.Size = New Size(120, 20)
        lblProfitTitle.Location = New Point(5, 5)
        profitPanel.Controls.Add(lblProfitTitle)

        Dim lblProfit As New Label()
        lblProfit.Name = "lblProfit"
        lblProfit.Text = "Profit: Rp 0"
        lblProfit.Font = New Font("Segoe UI", 9)
        lblProfit.ForeColor = Color.FromArgb(46, 204, 113)
        lblProfit.Size = New Size(270, 15)
        lblProfit.Location = New Point(5, 25)
        profitPanel.Controls.Add(lblProfit)

        Dim lblProfitPersen As New Label()
        lblProfitPersen.Name = "lblProfitPersen"
        lblProfitPersen.Text = "Persentase: 0%"
        lblProfitPersen.Font = New Font("Segoe UI", 9)
        lblProfitPersen.ForeColor = Color.FromArgb(46, 204, 113)
        lblProfitPersen.Size = New Size(270, 15)
        lblProfitPersen.Location = New Point(5, 40)
        profitPanel.Controls.Add(lblProfitPersen)

        yPos += 80

        ' Buttons Panel
        Dim buttonPanel As New Panel()
        buttonPanel.Size = New Size(280, 50)
        buttonPanel.Location = New Point(10, yPos)
        parent.Controls.Add(buttonPanel)

        ' Save Button
        Dim btnSave As New Button()
        btnSave.Name = "btnSimpan"
        btnSave.Text = "SIMPAN"
        btnSave.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        btnSave.Size = New Size(90, 35)
        btnSave.Location = New Point(0, 0)
        btnSave.BackColor = Color.FromArgb(46, 204, 113)
        btnSave.ForeColor = Color.White
        btnSave.FlatStyle = FlatStyle.Flat
        btnSave.FlatAppearance.BorderSize = 0
        btnSave.Cursor = Cursors.Hand
        AddHandler btnSave.Click, AddressOf BtnSimpan_Click
        buttonPanel.Controls.Add(btnSave)

        ' Update Button
        Dim btnUpdate As New Button()
        btnUpdate.Name = "btnUpdate"
        btnUpdate.Text = "UPDATE"
        btnUpdate.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        btnUpdate.Size = New Size(90, 35)
        btnUpdate.Location = New Point(95, 0)
        btnUpdate.BackColor = Color.FromArgb(52, 152, 219)
        btnUpdate.ForeColor = Color.White
        btnUpdate.FlatStyle = FlatStyle.Flat
        btnUpdate.FlatAppearance.BorderSize = 0
        btnUpdate.Cursor = Cursors.Hand
        AddHandler btnUpdate.Click, AddressOf BtnUpdate_Click
        buttonPanel.Controls.Add(btnUpdate)

        ' Cancel Button
        Dim btnCancel As New Button()
        btnCancel.Name = "btnBatal"
        btnCancel.Text = "BATAL"
        btnCancel.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        btnCancel.Size = New Size(90, 35)
        btnCancel.Location = New Point(190, 0)
        btnCancel.BackColor = Color.FromArgb(149, 165, 166)
        btnCancel.ForeColor = Color.White
        btnCancel.FlatStyle = FlatStyle.Flat
        btnCancel.FlatAppearance.BorderSize = 0
        btnCancel.Cursor = Cursors.Hand
        AddHandler btnCancel.Click, AddressOf BtnBatal_Click
        buttonPanel.Controls.Add(btnCancel)

        ' Add event handlers for profit calculation
        AddHandler txtHargaBeli.TextChanged, AddressOf CalculateProfit
        AddHandler txtHargaJual.TextChanged, AddressOf CalculateProfit
    End Sub

    Private Sub CreateDataGridControls(parent As Panel)
        ' Title
        Dim lblTitle As New Label()
        lblTitle.Text = "DAFTAR PRODUK"
        lblTitle.Font = New Font("Segoe UI", 14, FontStyle.Bold)
        lblTitle.ForeColor = Color.FromArgb(52, 73, 94)
        lblTitle.Size = New Size(300, 30)
        lblTitle.Location = New Point(10, 10)
        parent.Controls.Add(lblTitle)

        ' Filter Panel
        Dim filterPanel As New Panel()
        filterPanel.Size = New Size(parent.Width - 30, 80)
        filterPanel.Location = New Point(10, 50)
        parent.Controls.Add(filterPanel)

        ' Search
        Dim lblSearch As New Label()
        lblSearch.Text = "Cari:"
        lblSearch.Font = New Font("Segoe UI", 10)
        lblSearch.Size = New Size(40, 25)
        lblSearch.Location = New Point(0, 8)
        filterPanel.Controls.Add(lblSearch)

        Dim txtSearch As New TextBox()
        txtSearch.Name = "txtCari"
        txtSearch.Font = New Font("Segoe UI", 10)
        txtSearch.Size = New Size(200, 25)
        txtSearch.Location = New Point(45, 5)
        txtSearch.BorderStyle = BorderStyle.FixedSingle
        AddHandler txtSearch.TextChanged, AddressOf TxtCari_TextChanged
        filterPanel.Controls.Add(txtSearch)

        ' Filter Kategori
        Dim lblFilterKategori As New Label()
        lblFilterKategori.Text = "Kategori:"
        lblFilterKategori.Font = New Font("Segoe UI", 10)
        lblFilterKategori.Size = New Size(60, 25)
        lblFilterKategori.Location = New Point(260, 8)
        filterPanel.Controls.Add(lblFilterKategori)

        Dim cmbFilterKategori As New ComboBox()
        cmbFilterKategori.Name = "cmbFilterKategori"
        cmbFilterKategori.Font = New Font("Segoe UI", 10)
        cmbFilterKategori.Size = New Size(150, 25)
        cmbFilterKategori.Location = New Point(325, 5)
        cmbFilterKategori.DropDownStyle = ComboBoxStyle.DropDownList
        AddHandler cmbFilterKategori.SelectedIndexChanged, AddressOf FilterData
        filterPanel.Controls.Add(cmbFilterKategori)

        ' Filter Status Stok
        Dim lblFilterStok As New Label()
        lblFilterStok.Text = "Status Stok:"
        lblFilterStok.Font = New Font("Segoe UI", 10)
        lblFilterStok.Size = New Size(80, 25)
        lblFilterStok.Location = New Point(0, 38)
        filterPanel.Controls.Add(lblFilterStok)

        Dim cmbFilterStok As New ComboBox()
        cmbFilterStok.Name = "cmbFilterStok"
        cmbFilterStok.Font = New Font("Segoe UI", 10)
        cmbFilterStok.Size = New Size(120, 25)
        cmbFilterStok.Location = New Point(85, 35)
        cmbFilterStok.DropDownStyle = ComboBoxStyle.DropDownList
        cmbFilterStok.Items.AddRange({"Semua", "Stok Aman", "Stok Menipis", "Stok Habis"})
        cmbFilterStok.SelectedIndex = 0
        AddHandler cmbFilterStok.SelectedIndexChanged, AddressOf FilterData
        filterPanel.Controls.Add(cmbFilterStok)

        Dim btnRefresh As New Button()
        btnRefresh.Name = "btnRefresh"
        btnRefresh.Text = "REFRESH"
        btnRefresh.Font = New Font("Segoe UI", 9, FontStyle.Bold)
        btnRefresh.Size = New Size(80, 25)
        btnRefresh.Location = New Point(220, 35)
        btnRefresh.BackColor = Color.FromArgb(52, 152, 219)
        btnRefresh.ForeColor = Color.White
        btnRefresh.FlatStyle = FlatStyle.Flat
        btnRefresh.FlatAppearance.BorderSize = 0
        btnRefresh.Cursor = Cursors.Hand
        AddHandler btnRefresh.Click, AddressOf BtnRefresh_Click
        filterPanel.Controls.Add(btnRefresh)

        ' DataGridView
        Dim dgv As New DataGridView()
        dgv.Name = "dgvProduk"
        dgv.Size = New Size(parent.Width - 30, parent.Height - 180)
        dgv.Location = New Point(10, 140)
        dgv.Anchor = AnchorStyles.Top Or AnchorStyles.Bottom Or AnchorStyles.Left Or AnchorStyles.Right
        parent.Controls.Add(dgv)

        ' Action Buttons Panel
        Dim actionPanel As New Panel()
        actionPanel.Size = New Size(parent.Width - 30, 40)
        actionPanel.Location = New Point(10, parent.Height - 50)
        actionPanel.Anchor = AnchorStyles.Bottom Or AnchorStyles.Left Or AnchorStyles.Right
        parent.Controls.Add(actionPanel)

        Dim btnEdit As New Button()
        btnEdit.Name = "btnEdit"
        btnEdit.Text = "EDIT"
        btnEdit.Font = New Font("Segoe UI", 9, FontStyle.Bold)
        btnEdit.Size = New Size(70, 30)
        btnEdit.Location = New Point(0, 5)
        btnEdit.BackColor = Color.FromArgb(243, 156, 18)
        btnEdit.ForeColor = Color.White
        btnEdit.FlatStyle = FlatStyle.Flat
        btnEdit.FlatAppearance.BorderSize = 0
        btnEdit.Cursor = Cursors.Hand
        AddHandler btnEdit.Click, AddressOf BtnEdit_Click
        actionPanel.Controls.Add(btnEdit)

        Dim btnDelete As New Button()
        btnDelete.Name = "btnHapus"
        btnDelete.Text = "HAPUS"
        btnDelete.Font = New Font("Segoe UI", 9, FontStyle.Bold)
        btnDelete.Size = New Size(70, 30)
        btnDelete.Location = New Point(75, 5)
        btnDelete.BackColor = Color.FromArgb(231, 76, 60)
        btnDelete.ForeColor = Color.White
        btnDelete.FlatStyle = FlatStyle.Flat
        btnDelete.FlatAppearance.BorderSize = 0
        btnDelete.Cursor = Cursors.Hand
        AddHandler btnDelete.Click, AddressOf BtnHapus_Click
        actionPanel.Controls.Add(btnDelete)

        Dim btnExport As New Button()
        btnExport.Name = "btnExport"
        btnExport.Text = "EXPORT"
        btnExport.Font = New Font("Segoe UI", 9, FontStyle.Bold)
        btnExport.Size = New Size(70, 30)
        btnExport.Location = New Point(150, 5)
        btnExport.BackColor = Color.FromArgb(155, 89, 182)
        btnExport.ForeColor = Color.White
        btnExport.FlatStyle = FlatStyle.Flat
        btnExport.FlatAppearance.BorderSize = 0
        btnExport.Cursor = Cursors.Hand
        AddHandler btnExport.Click, AddressOf BtnExport_Click
        actionPanel.Controls.Add(btnExport)
    End Sub

    Private Sub SetupDataGridView()
        Dim dgv As DataGridView = DirectCast(Me.Controls.Find("dgvProduk", True)(0), DataGridView)

        ' Set DataGridView properties
        dgv.AllowUserToAddRows = False
        dgv.AllowUserToDeleteRows = False
        dgv.ReadOnly = True
        dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgv.MultiSelect = False
        dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        dgv.BackgroundColor = Color.White
        dgv.BorderStyle = BorderStyle.None
        dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal
        dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219)
        dgv.DefaultCellStyle.SelectionForeColor = Color.White
        dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94)
        dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White
        dgv.ColumnHeadersDefaultCellStyle.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        dgv.EnableHeadersVisualStyles = False
        dgv.RowHeadersVisible = False

        ' Add double click event
        AddHandler dgv.CellDoubleClick, AddressOf DgvProduk_CellDoubleClick
    End Sub

    Private Sub LoadKategoriComboBox()
        Try
            Dim listKategori As List(Of Kategori) = kategoriRepo.GetAllKategori()

            ' Load kategori untuk form input
            Dim cmbKategori As ComboBox = DirectCast(Me.Controls.Find("cmbKategori", True)(0), ComboBox)
            cmbKategori.DataSource = Nothing
            cmbKategori.Items.Clear()
            cmbKategori.DisplayMember = "NamaKategori"
            cmbKategori.ValueMember = "IdKategori"
            cmbKategori.DataSource = listKategori

            ' Load kategori untuk filter
            Dim cmbFilterKategori As ComboBox = DirectCast(Me.Controls.Find("cmbFilterKategori", True)(0), ComboBox)
            cmbFilterKategori.Items.Clear()
            cmbFilterKategori.Items.Add(New With {.IdKategori = 0, .NamaKategori = "Semua Kategori"})
            For Each kategori In listKategori
                cmbFilterKategori.Items.Add(kategori)
            Next
            cmbFilterKategori.DisplayMember = "NamaKategori"
            cmbFilterKategori.ValueMember = "IdKategori"
            cmbFilterKategori.SelectedIndex = 0

        Catch ex As Exception
            MessageBox.Show("Error loading kategori: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadProdukData()
        Try
            Dim dgv As DataGridView = DirectCast(Me.Controls.Find("dgvProduk", True)(0), DataGridView)
            Dim listProduk As List(Of Produk) = produkRepo.GetAllProduk()

            ' Create DataTable
            Dim dt As New DataTable()
            dt.Columns.Add("ID", GetType(Integer))
            dt.Columns.Add("Kode", GetType(String))
            dt.Columns.Add("Nama Produk", GetType(String))
            dt.Columns.Add("Kategori", GetType(String))
            dt.Columns.Add("Harga Beli", GetType(Decimal))
            dt.Columns.Add("Harga Jual", GetType(Decimal))
            dt.Columns.Add("Stok", GetType(Integer))
            dt.Columns.Add("Stok Min", GetType(Integer))
            dt.Columns.Add("Status Stok", GetType(String))
            dt.Columns.Add("Profit", GetType(Decimal))
            dt.Columns.Add("Profit %", GetType(String))

            ' Fill DataTable
            For Each produk In listProduk
                dt.Rows.Add(
                    produk.IdProduk,
                    produk.KodeProduk,
                    produk.NamaProduk,
                    produk.NamaKategori,
                    produk.HargaBeli,
                    produk.HargaJual,
                    produk.Stok,
                    produk.StokMinimum,
                    produk.StatusStok,
                    produk.Profit,
                    produk.PersentaseProfit.ToString("F1") & "%"
                )
            Next

            dgv.DataSource = dt

            ' Hide ID column and format columns
            If dgv.Columns.Count > 0 Then
                dgv.Columns("ID").Visible = False
                dgv.Columns("Harga Beli").DefaultCellStyle.Format = "C0"
                dgv.Columns("Harga Jual").DefaultCellStyle.Format = "C0"
                dgv.Columns("Profit").DefaultCellStyle.Format = "C0"
                dgv.Columns("Harga Beli").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                dgv.Columns("Harga Jual").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                dgv.Columns("Profit").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                dgv.Columns("Stok").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter
                dgv.Columns("Stok Min").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter
                dgv.Columns("Profit %").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter

                ' Color coding for status stok
                For Each row As DataGridViewRow In dgv.Rows
                    Dim statusStok As String = row.Cells("Status Stok").Value.ToString()
                    Select Case statusStok
                        Case "Stok Habis"
                            row.Cells("Status Stok").Style.BackColor = Color.FromArgb(231, 76, 60)
                            row.Cells("Status Stok").Style.ForeColor = Color.White
                        Case "Stok Menipis"
                            row.Cells("Status Stok").Style.BackColor = Color.FromArgb(243, 156, 18)
                            row.Cells("Status Stok").Style.ForeColor = Color.White
                        Case "Stok Aman"
                            row.Cells("Status Stok").Style.BackColor = Color.FromArgb(46, 204, 113)
                            row.Cells("Status Stok").Style.ForeColor = Color.White
                    End Select
                Next
            End If

        Catch ex As Exception
            MessageBox.Show("Error loading data: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetFormMode(editMode As Boolean)
        isEditMode = editMode

        Dim btnSimpan As Button = DirectCast(Me.Controls.Find("btnSimpan", True)(0), Button)
        Dim btnUpdate As Button = DirectCast(Me.Controls.Find("btnUpdate", True)(0), Button)

        btnSimpan.Visible = Not editMode
        btnUpdate.Visible = editMode

        If Not editMode Then
            ClearForm()
            selectedProdukId = 0
        End If
    End Sub

    Private Sub ClearForm()
        Dim txtKode As TextBox = DirectCast(Me.Controls.Find("txtKodeProduk", True)(0), TextBox)
        Dim txtNama As TextBox = DirectCast(Me.Controls.Find("txtNamaProduk", True)(0), TextBox)
        Dim cmbKategori As ComboBox = DirectCast(Me.Controls.Find("cmbKategori", True)(0), ComboBox)
        Dim txtHargaBeli As TextBox = DirectCast(Me.Controls.Find("txtHargaBeli", True)(0), TextBox)
        Dim txtHargaJual As TextBox = DirectCast(Me.Controls.Find("txtHargaJual", True)(0), TextBox)
        Dim txtStok As NumericUpDown = DirectCast(Me.Controls.Find("txtStok", True)(0), NumericUpDown)
        Dim txtStokMin As NumericUpDown = DirectCast(Me.Controls.Find("txtStokMinimum", True)(0), NumericUpDown)
        Dim txtDeskripsi As TextBox = DirectCast(Me.Controls.Find("txtDeskripsi", True)(0), TextBox)

        txtKode.Clear()
        txtNama.Clear()
        If cmbKategori.Items.Count > 0 Then cmbKategori.SelectedIndex = 0
        txtHargaBeli.Text = "0"
        txtHargaJual.Text = "0"
        txtStok.Value = 0
        txtStokMin.Value = 5
        txtDeskripsi.Clear()

        CalculateProfit(Nothing, Nothing)
        txtKode.Focus()
    End Sub

    Private Sub CalculateProfit(sender As Object, e As EventArgs)
        Try
            Dim txtHargaBeli As TextBox = DirectCast(Me.Controls.Find("txtHargaBeli", True)(0), TextBox)
            Dim txtHargaJual As TextBox = DirectCast(Me.Controls.Find("txtHargaJual", True)(0), TextBox)
            Dim lblProfit As Label = DirectCast(Me.Controls.Find("lblProfit", True)(0), Label)
            Dim lblProfitPersen As Label = DirectCast(Me.Controls.Find("lblProfitPersen", True)(0), Label)

            Dim hargaBeli As Decimal = 0
            Dim hargaJual As Decimal = 0

            Decimal.TryParse(txtHargaBeli.Text.Replace("Rp", "").Replace(".", "").Replace(",", "").Trim(), hargaBeli)
            Decimal.TryParse(txtHargaJual.Text.Replace("Rp", "").Replace(".", "").Replace(",", "").Trim(), hargaJual)

            Dim profit As Decimal = hargaJual - hargaBeli
            Dim profitPersen As Decimal = 0

            If hargaBeli > 0 Then
                profitPersen = (profit / hargaBeli) * 100
            End If

            lblProfit.Text = $"Profit: {profit:C0}"
            lblProfitPersen.Text = $"Persentase: {profitPersen:F1}%"

            ' Color coding
            If profit > 0 Then
                lblProfit.ForeColor = Color.FromArgb(46, 204, 113)
                lblProfitPersen.ForeColor = Color.FromArgb(46, 204, 113)
            ElseIf profit < 0 Then
                lblProfit.ForeColor = Color.FromArgb(231, 76, 60)
                lblProfitPersen.ForeColor = Color.FromArgb(231, 76, 60)
            Else
                lblProfit.ForeColor = Color.FromArgb(149, 165, 166)
                lblProfitPersen.ForeColor = Color.FromArgb(149, 165, 166)
            End If

        Catch ex As Exception
            ' Handle error silently for calculation
        End Try
    End Sub

    Private Sub NumericTextBox_KeyPress(sender As Object, e As KeyPressEventArgs)
        ' Allow only numbers, backspace, and decimal point
        If Not Char.IsDigit(e.KeyChar) AndAlso e.KeyChar <> Chr(8) AndAlso e.KeyChar <> "."c Then
            e.Handled = True
        End If

        ' Allow only one decimal point
        Dim textBox As TextBox = DirectCast(sender, TextBox)
        If e.KeyChar = "."c AndAlso textBox.Text.Contains(".") Then
            e.Handled = True
        End If
    End Sub

    Private Sub FormatCurrency_Leave(sender As Object, e As EventArgs)
        Dim textBox As TextBox = DirectCast(sender, TextBox)
        Dim value As Decimal = 0

        If Decimal.TryParse(textBox.Text.Replace("Rp", "").Replace(".", "").Replace(",", "").Trim(), value) Then
            textBox.Text = value.ToString("N0")
        Else
            textBox.Text = "0"
        End If
    End Sub

    Private Sub BtnSimpan_Click(sender As Object, e As EventArgs)
        Try
            Dim produk As New Produk()

            ' Get form values
            Dim txtKode As TextBox = DirectCast(Me.Controls.Find("txtKodeProduk", True)(0), TextBox)
            Dim txtNama As TextBox = DirectCast(Me.Controls.Find("txtNamaProduk", True)(0), TextBox)
            Dim cmbKategori As ComboBox = DirectCast(Me.Controls.Find("cmbKategori", True)(0), ComboBox)
            Dim txtHargaBeli As TextBox = DirectCast(Me.Controls.Find("txtHargaBeli", True)(0), TextBox)
            Dim txtHargaJual As TextBox = DirectCast(Me.Controls.Find("txtHargaJual", True)(0), TextBox)
            Dim txtStok As NumericUpDown = DirectCast(Me.Controls.Find("txtStok", True)(0), NumericUpDown)
            Dim txtStokMin As NumericUpDown = DirectCast(Me.Controls.Find("txtStokMinimum", True)(0), NumericUpDown)
            Dim txtDeskripsi As TextBox = DirectCast(Me.Controls.Find("txtDeskripsi", True)(0), TextBox)

            produk.KodeProduk = txtKode.Text.Trim()
            produk.NamaProduk = txtNama.Text.Trim()
            produk.IdKategori = If(cmbKategori.SelectedValue IsNot Nothing, Convert.ToInt32(cmbKategori.SelectedValue), 0)

            Decimal.TryParse(txtHargaBeli.Text.Replace("Rp", "").Replace(".", "").Replace(",", "").Trim(), produk.HargaBeli)
            Decimal.TryParse(txtHargaJual.Text.Replace("Rp", "").Replace(".", "").Replace(",", "").Trim(), produk.HargaJual)

            produk.Stok = Convert.ToInt32(txtStok.Value)
            produk.StokMinimum = Convert.ToInt32(txtStokMin.Value)
            produk.Deskripsi = txtDeskripsi.Text.Trim()

            Dim result As DatabaseResponse = produkRepo.TambahProduk(produk)

            If result.Success Then
                MessageBox.Show(result.Message, "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadProdukData()
                ClearForm()
            Else
                MessageBox.Show(result.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If

        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnUpdate_Click(sender As Object, e As EventArgs)
        Try
            Dim produk As New Produk()
            produk.IdProduk = selectedProdukId

            ' Get form values
            Dim txtKode As TextBox = DirectCast(Me.Controls.Find("txtKodeProduk", True)(0), TextBox)
            Dim txtNama As TextBox = DirectCast(Me.Controls.Find("txtNamaProduk", True)(0), TextBox)
            Dim cmbKategori As ComboBox = DirectCast(Me.Controls.Find("cmbKategori", True)(0), ComboBox)
            Dim txtHargaBeli As TextBox = DirectCast(Me.Controls.Find("txtHargaBeli", True)(0), TextBox)
            Dim txtHargaJual As TextBox = DirectCast(Me.Controls.Find("txtHargaJual", True)(0), TextBox)
            Dim txtStok As NumericUpDown = DirectCast(Me.Controls.Find("txtStok", True)(0), NumericUpDown)
            Dim txtStokMin As NumericUpDown = DirectCast(Me.Controls.Find("txtStokMinimum", True)(0), NumericUpDown)
            Dim txtDeskripsi As TextBox = DirectCast(Me.Controls.Find("txtDeskripsi", True)(0), TextBox)

            produk.KodeProduk = txtKode.Text.Trim()
            produk.NamaProduk = txtNama.Text.Trim()
            produk.IdKategori = If(cmbKategori.SelectedValue IsNot Nothing, Convert.ToInt32(cmbKategori.SelectedValue), 0)

            Decimal.TryParse(txtHargaBeli.Text.Replace("Rp", "").Replace(".", "").Replace(",", "").Trim(), produk.HargaBeli)
            Decimal.TryParse(txtHargaJual.Text.Replace("Rp", "").Replace(".", "").Replace(",", "").Trim(), produk.HargaJual)

            produk.Stok = Convert.ToInt32(txtStok.Value)
            produk.StokMinimum = Convert.ToInt32(txtStokMin.Value)
            produk.Deskripsi = txtDeskripsi.Text.Trim()

            Dim result As DatabaseResponse = produkRepo.UpdateProduk(produk)

            If result.Success Then
                MessageBox.Show(result.Message, "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadProdukData()
                SetFormMode(False)
            Else
                MessageBox.Show(result.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If

        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnBatal_Click(sender As Object, e As EventArgs)
        SetFormMode(False)
    End Sub

    Private Sub BtnEdit_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = DirectCast(Me.Controls.Find("dgvProduk", True)(0), DataGridView)

        If dgv.SelectedRows.Count > 0 Then
            Dim selectedRow As DataGridViewRow = dgv.SelectedRows(0)
            selectedProdukId = Convert.ToInt32(selectedRow.Cells("ID").Value)

            ' Fill form with selected data
            Dim txtKode As TextBox = DirectCast(Me.Controls.Find("txtKodeProduk", True)(0), TextBox)
            Dim txtNama As TextBox = DirectCast(Me.Controls.Find("txtNamaProduk", True)(0), TextBox)
            Dim cmbKategori As ComboBox = DirectCast(Me.Controls.Find("cmbKategori", True)(0), ComboBox)
            Dim txtHargaBeli As TextBox = DirectCast(Me.Controls.Find("txtHargaBeli", True)(0), TextBox)
            Dim txtHargaJual As TextBox = DirectCast(Me.Controls.Find("txtHargaJual", True)(0), TextBox)
            Dim txtStok As NumericUpDown = DirectCast(Me.Controls.Find("txtStok", True)(0), NumericUpDown)
            Dim txtStokMin As NumericUpDown = DirectCast(Me.Controls.Find("txtStokMinimum", True)(0), NumericUpDown)
            Dim txtDeskripsi As TextBox = DirectCast(Me.Controls.Find("txtDeskripsi", True)(0), TextBox)

            ' Get full product data
            Dim produk As Produk = produkRepo.GetProdukById(selectedProdukId)

            txtKode.Text = produk.KodeProduk
            txtNama.Text = produk.NamaProduk
            txtHargaBeli.Text = produk.HargaBeli.ToString("N0")
            txtHargaJual.Text = produk.HargaJual.ToString("N0")
            txtStok.Value = produk.Stok
            txtStokMin.Value = produk.StokMinimum
            txtDeskripsi.Text = produk.Deskripsi

            ' Set kategori
            For i As Integer = 0 To cmbKategori.Items.Count - 1
                Dim kategori As Kategori = DirectCast(cmbKategori.Items(i), Kategori)
                If kategori.IdKategori = produk.IdKategori Then
                    cmbKategori.SelectedIndex = i
                    Exit For
                End If
            Next

            SetFormMode(True)
        Else
            MessageBox.Show("Pilih produk yang akan diedit!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub BtnHapus_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = DirectCast(Me.Controls.Find("dgvProduk", True)(0), DataGridView)

        If dgv.SelectedRows.Count > 0 Then
            Dim selectedRow As DataGridViewRow = dgv.SelectedRows(0)
            Dim namaProduk As String = selectedRow.Cells("Nama Produk").Value.ToString()

            If MessageBox.Show($"Apakah Anda yakin ingin menghapus produk '{namaProduk}'?", "Konfirmasi Hapus", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                Dim id As Integer = Convert.ToInt32(selectedRow.Cells("ID").Value)
                Dim result As DatabaseResponse = produkRepo.HapusProduk(id)

                If result.Success Then
                    MessageBox.Show(result.Message, "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    LoadProdukData()
                    SetFormMode(False)
                Else
                    MessageBox.Show(result.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End If
            End If
        Else
            MessageBox.Show("Pilih produk yang akan dihapus!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        LoadProdukData()
        SetFormMode(False)

        ' Reset filters
        Dim txtCari As TextBox = DirectCast(Me.Controls.Find("txtCari", True)(0), TextBox)
        Dim cmbFilterKategori As ComboBox = DirectCast(Me.Controls.Find("cmbFilterKategori", True)(0), ComboBox)
        Dim cmbFilterStok As ComboBox = DirectCast(Me.Controls.Find("cmbFilterStok", True)(0), ComboBox)

        txtCari.Clear()
        cmbFilterKategori.SelectedIndex = 0
        cmbFilterStok.SelectedIndex = 0
    End Sub

    Private Sub TxtCari_TextChanged(sender As Object, e As EventArgs)
        FilterData(Nothing, Nothing)
    End Sub

    Private Sub FilterData(sender As Object, e As EventArgs)
        Try
            Dim txtCari As TextBox = DirectCast(Me.Controls.Find("txtCari", True)(0), TextBox)
            Dim cmbFilterKategori As ComboBox = DirectCast(Me.Controls.Find("cmbFilterKategori", True)(0), ComboBox)
            Dim cmbFilterStok As ComboBox = DirectCast(Me.Controls.Find("cmbFilterStok", True)(0), ComboBox)
            Dim dgv As DataGridView = DirectCast(Me.Controls.Find("dgvProduk", True)(0), DataGridView)

            If dgv.DataSource IsNot Nothing Then
                Dim dt As DataTable = DirectCast(dgv.DataSource, DataTable)
                Dim filter As String = ""

                ' Filter berdasarkan pencarian
                If Not String.IsNullOrWhiteSpace(txtCari.Text) Then
                    filter = $"([Nama Produk] LIKE '%{txtCari.Text}%' OR [Kode] LIKE '%{txtCari.Text}%')"
                End If

                ' Filter berdasarkan kategori
                If cmbFilterKategori.SelectedValue IsNot Nothing AndAlso Convert.ToInt32(cmbFilterKategori.SelectedValue) > 0 Then
                    Dim kategoriFilter As String = $"[Kategori] = '{DirectCast(cmbFilterKategori.SelectedItem, Kategori).NamaKategori}'"
                    If Not String.IsNullOrEmpty(filter) Then
                        filter &= " AND " & kategoriFilter
                    Else
                        filter = kategoriFilter
                    End If
                End If

                ' Filter berdasarkan status stok
                If cmbFilterStok.SelectedIndex > 0 Then
                    Dim stokFilter As String = $"[Status Stok] = '{cmbFilterStok.SelectedItem.ToString()}'"
                    If Not String.IsNullOrEmpty(filter) Then
                        filter &= " AND " & stokFilter
                    Else
                        filter = stokFilter
                    End If
                End If

                dt.DefaultView.RowFilter = filter
            End If

        Catch ex As Exception
            ' Handle filter error silently
        End Try
    End Sub

    Private Sub DgvProduk_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs)
        If e.RowIndex >= 0 Then
            BtnEdit_Click(Nothing, Nothing)
        End If
    End Sub

    Private Sub BtnExport_Click(sender As Object, e As EventArgs)
        Try
            Dim dgv As DataGridView = DirectCast(Me.Controls.Find("dgvProduk", True)(0), DataGridView)

            If dgv.Rows.Count = 0 Then
                MessageBox.Show("Tidak ada data untuk diekspor!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            Dim saveDialog As New SaveFileDialog()
            saveDialog.Filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*"
            saveDialog.FilterIndex = 1
            saveDialog.FileName = $"Data_Produk_{DateTime.Now:yyyyMMdd_HHmmss}.csv"

            If saveDialog.ShowDialog() = DialogResult.OK Then
                ExportToCSV(dgv, saveDialog.FileName)
                MessageBox.Show("Data berhasil diekspor!", "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        Catch ex As Exception
            MessageBox.Show("Error saat ekspor: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ExportToCSV(dgv As DataGridView, fileName As String)
        Dim csv As New System.Text.StringBuilder()

        ' Header
        Dim headers As New List(Of String)
        For Each column As DataGridViewColumn In dgv.Columns
            If column.Visible Then
                headers.Add(column.HeaderText)
            End If
        Next
        csv.AppendLine(String.Join(",", headers))

        ' Data rows
        For Each row As DataGridViewRow In dgv.Rows
            If Not row.IsNewRow Then
                Dim values As New List(Of String)
                For Each column As DataGridViewColumn In dgv.Columns
                    If column.Visible Then
                        Dim value As String = If(row.Cells(column.Index).Value?.ToString(), "")
                        ' Escape commas and quotes
                        If value.Contains(",") OrElse value.Contains("""") Then
                            value = """" & value.Replace("""", """""") & """"
                        End If
                        values.Add(value)
                    End If
                Next
                csv.AppendLine(String.Join(",", values))
            End If
        Next

        System.IO.File.WriteAllText(fileName, csv.ToString(), System.Text.Encoding.UTF8)
    End Sub
End Class
