' =====================================================
' Model Classes untuk Aplikasi Toko/Penjualan
' =====================================================

Public Class Kategori
    Public Property IdKategori As Integer
    Public Property NamaKategori As String
    Public Property Deskripsi As String
    Public Property TanggalDibuat As DateTime
    Public Property TanggalDiubah As DateTime
    Public Property StatusAktif As Boolean

    Public Sub New()
        IdKategori = 0
        NamaKategori = ""
        Deskripsi = ""
        TanggalDibuat = DateTime.Now
        TanggalDiubah = DateTime.Now
        StatusAktif = True
    End Sub

    Public Sub New(id As Integer, nama As String, desk As String)
        IdKategori = id
        NamaKategori = nama
        Deskripsi = desk
        TanggalDibuat = DateTime.Now
        TanggalDiubah = DateTime.Now
        StatusAktif = True
    End Sub
End Class

Public Class Produk
    Public Property IdProduk As Integer
    Public Property KodeProduk As String
    Public Property NamaProduk As String
    Public Property Deskripsi As String
    Public Property HargaBeli As Decimal
    Public Property HargaJual As Decimal
    Public Property Stok As Integer
    Public Property StokMinimum As Integer
    Public Property IdKategori As Integer
    Public Property NamaKategori As String ' Untuk join dengan tabel kategori
    Public Property TanggalDibuat As DateTime
    Public Property TanggalDiubah As DateTime
    Public Property StatusAktif As Boolean

    Public Sub New()
        IdProduk = 0
        KodeProduk = ""
        NamaProduk = ""
        Deskripsi = ""
        HargaBeli = 0
        HargaJual = 0
        Stok = 0
        StokMinimum = 5
        IdKategori = 0
        NamaKategori = ""
        TanggalDibuat = DateTime.Now
        TanggalDiubah = DateTime.Now
        StatusAktif = True
    End Sub

    ' Property untuk menghitung profit
    Public ReadOnly Property Profit As Decimal
        Get
            Return HargaJual - HargaBeli
        End Get
    End Property

    ' Property untuk menghitung persentase profit
    Public ReadOnly Property PersentaseProfit As Decimal
        Get
            If HargaBeli > 0 Then
                Return ((HargaJual - HargaBeli) / HargaBeli) * 100
            Else
                Return 0
            End If
        End Get
    End Property

    ' Property untuk status stok
    Public ReadOnly Property StatusStok As String
        Get
            If Stok = 0 Then
                Return "Stok Habis"
            ElseIf Stok <= StokMinimum Then
                Return "Stok Menipis"
            Else
                Return "Stok Aman"
            End If
        End Get
    End Property
End Class

Public Class User
    Public Property IdUser As Integer
    Public Property Username As String
    Public Property Password As String
    Public Property NamaLengkap As String
    Public Property Email As String
    Public Property Role As String
    Public Property TanggalDibuat As DateTime
    Public Property TanggalLoginTerakhir As DateTime?
    Public Property StatusAktif As Boolean

    Public Sub New()
        IdUser = 0
        Username = ""
        Password = ""
        NamaLengkap = ""
        Email = ""
        Role = "kasir"
        TanggalDibuat = DateTime.Now
        TanggalLoginTerakhir = Nothing
        StatusAktif = True
    End Sub

    Public Sub New(username As String, password As String, namaLengkap As String, role As String)
        Me.Username = username
        Me.Password = password
        Me.NamaLengkap = namaLengkap
        Me.Role = role
        Me.Email = ""
        Me.TanggalDibuat = DateTime.Now
        Me.TanggalLoginTerakhir = Nothing
        Me.StatusAktif = True
    End Sub
End Class

' Class untuk menyimpan informasi user yang sedang login
Public Class CurrentUser
    Private Shared _instance As CurrentUser
    Private Shared ReadOnly _lock As New Object()

    Public Property IdUser As Integer
    Public Property Username As String
    Public Property NamaLengkap As String
    Public Property Role As String
    Public Property WaktuLogin As DateTime

    Private Sub New()
        ' Private constructor untuk Singleton pattern
        ' Initialize default values
        IdUser = 0
        Username = ""
        NamaLengkap = ""
        Role = ""
        WaktuLogin = DateTime.Now
    End Sub

    Public Shared ReadOnly Property Instance As CurrentUser
        Get
            If _instance Is Nothing Then
                SyncLock _lock
                    If _instance Is Nothing Then
                        _instance = New CurrentUser()
                        System.Diagnostics.Debug.WriteLine("CurrentUser singleton instance created")
                    End If
                End SyncLock
            End If
            Return _instance
        End Get
    End Property

    Public Sub SetUser(user As User)
        System.Diagnostics.Debug.WriteLine($"SetUser called - Input: ID={user.IdUser}, Username={user.Username}, Role={user.Role}")
        IdUser = user.IdUser
        Username = user.Username
        NamaLengkap = user.NamaLengkap
        Role = user.Role
        WaktuLogin = DateTime.Now
        System.Diagnostics.Debug.WriteLine($"SetUser completed - Current: ID={IdUser}, Username={Username}, Role={Role}, IsLoggedIn={IsLoggedIn()}")
    End Sub

    Public Sub ClearUser()
        IdUser = 0
        Username = ""
        NamaLengkap = ""
        Role = ""
        WaktuLogin = Nothing
    End Sub

    Public Function IsLoggedIn() As Boolean
        Dim result As Boolean = IdUser > 0 AndAlso Not String.IsNullOrEmpty(Username)
        System.Diagnostics.Debug.WriteLine($"IsLoggedIn check - ID={IdUser}, Username='{Username}', Result={result}")
        Return result
    End Function

    Public Function IsAdmin() As Boolean
        Return Not String.IsNullOrEmpty(Role) AndAlso Role.ToLower() = "admin"
    End Function

    Public Function IsManager() As Boolean
        Return Not String.IsNullOrEmpty(Role) AndAlso (Role.ToLower() = "manager" OrElse IsAdmin())
    End Function
End Class

' Enum untuk Role User
Public Enum UserRole
    Admin
    Manager
    Kasir
End Enum

' Class untuk Response dari operasi database
Public Class DatabaseResponse
    Public Property Success As Boolean
    Public Property Message As String
    Public Property Data As Object

    Public Sub New()
        Success = False
        Message = ""
        Data = Nothing
    End Sub

    Public Sub New(success As Boolean, message As String, Optional data As Object = Nothing)
        Me.Success = success
        Me.Message = message
        Me.Data = data
    End Sub
End Class
