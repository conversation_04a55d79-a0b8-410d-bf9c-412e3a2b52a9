﻿Public Class FormKategori
    Private kategoriRepo As New KategoriRepository()
    Private isEditMode As Boolean = False
    Private selectedKategoriId As Integer = 0

    Private Sub FormKategori_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        InitializeForm()
        SetupDataGridView()
        LoadKategoriData()
        SetFormMode(False)
    End Sub

    Private Sub InitializeForm()
        ' Set form properties
        Me.Text = "Manajemen Kategori Produk"
        Me.Size = New Size(900, 600)
        Me.StartPosition = FormStartPosition.CenterParent
        Me.BackColor = Color.FromArgb(236, 240, 241)
        Me.MinimumSize = New Size(800, 500)

        CreateControls()
    End Sub

    Private Sub CreateControls()
        ' Main Panel
        Dim mainPanel As New TableLayoutPanel()
        mainPanel.Dock = DockStyle.Fill
        mainPanel.ColumnCount = 2
        mainPanel.RowCount = 1
        mainPanel.ColumnStyles.Add(New ColumnStyle(SizeType.Percent, 40))
        mainPanel.ColumnStyles.Add(New ColumnStyle(SizeType.Percent, 60))
        mainPanel.Padding = New Padding(10)
        Me.Controls.Add(mainPanel)

        ' Left Panel - Form Input
        Dim leftPanel As New Panel()
        leftPanel.BackColor = Color.White
        leftPanel.BorderStyle = BorderStyle.FixedSingle
        leftPanel.Padding = New Padding(15)
        mainPanel.Controls.Add(leftPanel, 0, 0)

        ' Right Panel - Data Grid
        Dim rightPanel As New Panel()
        rightPanel.BackColor = Color.White
        rightPanel.BorderStyle = BorderStyle.FixedSingle
        rightPanel.Padding = New Padding(15)
        mainPanel.Controls.Add(rightPanel, 1, 0)

        ' Create Left Panel Controls
        CreateInputControls(leftPanel)

        ' Create Right Panel Controls
        CreateDataGridControls(rightPanel)
    End Sub

    Private Sub CreateInputControls(parent As Panel)
        Dim yPos As Integer = 10

        ' Title
        Dim lblTitle As New Label()
        lblTitle.Text = "FORM KATEGORI"
        lblTitle.Font = New Font("Segoe UI", 14, FontStyle.Bold)
        lblTitle.ForeColor = Color.FromArgb(52, 73, 94)
        lblTitle.Size = New Size(300, 30)
        lblTitle.Location = New Point(10, yPos)
        parent.Controls.Add(lblTitle)
        yPos += 50

        ' Nama Kategori
        Dim lblNama As New Label()
        lblNama.Text = "Nama Kategori:"
        lblNama.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblNama.ForeColor = Color.FromArgb(52, 73, 94)
        lblNama.Size = New Size(120, 25)
        lblNama.Location = New Point(10, yPos)
        parent.Controls.Add(lblNama)
        yPos += 25

        Dim txtNama As New TextBox()
        txtNama.Name = "txtNamaKategori"
        txtNama.Font = New Font("Segoe UI", 10)
        txtNama.Size = New Size(280, 25)
        txtNama.Location = New Point(10, yPos)
        txtNama.BorderStyle = BorderStyle.FixedSingle
        parent.Controls.Add(txtNama)
        yPos += 40

        ' Deskripsi
        Dim lblDeskripsi As New Label()
        lblDeskripsi.Text = "Deskripsi:"
        lblDeskripsi.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblDeskripsi.ForeColor = Color.FromArgb(52, 73, 94)
        lblDeskripsi.Size = New Size(120, 25)
        lblDeskripsi.Location = New Point(10, yPos)
        parent.Controls.Add(lblDeskripsi)
        yPos += 25

        Dim txtDeskripsi As New TextBox()
        txtDeskripsi.Name = "txtDeskripsi"
        txtDeskripsi.Font = New Font("Segoe UI", 10)
        txtDeskripsi.Size = New Size(280, 80)
        txtDeskripsi.Location = New Point(10, yPos)
        txtDeskripsi.BorderStyle = BorderStyle.FixedSingle
        txtDeskripsi.Multiline = True
        txtDeskripsi.ScrollBars = ScrollBars.Vertical
        parent.Controls.Add(txtDeskripsi)
        yPos += 100

        ' Buttons Panel
        Dim buttonPanel As New Panel()
        buttonPanel.Size = New Size(280, 50)
        buttonPanel.Location = New Point(10, yPos)
        parent.Controls.Add(buttonPanel)

        ' Save Button
        Dim btnSave As New Button()
        btnSave.Name = "btnSimpan"
        btnSave.Text = "SIMPAN"
        btnSave.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        btnSave.Size = New Size(90, 35)
        btnSave.Location = New Point(0, 0)
        btnSave.BackColor = Color.FromArgb(46, 204, 113)
        btnSave.ForeColor = Color.White
        btnSave.FlatStyle = FlatStyle.Flat
        btnSave.FlatAppearance.BorderSize = 0
        btnSave.Cursor = Cursors.Hand
        AddHandler btnSave.Click, AddressOf BtnSimpan_Click
        buttonPanel.Controls.Add(btnSave)

        ' Update Button
        Dim btnUpdate As New Button()
        btnUpdate.Name = "btnUpdate"
        btnUpdate.Text = "UPDATE"
        btnUpdate.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        btnUpdate.Size = New Size(90, 35)
        btnUpdate.Location = New Point(95, 0)
        btnUpdate.BackColor = Color.FromArgb(52, 152, 219)
        btnUpdate.ForeColor = Color.White
        btnUpdate.FlatStyle = FlatStyle.Flat
        btnUpdate.FlatAppearance.BorderSize = 0
        btnUpdate.Cursor = Cursors.Hand
        AddHandler btnUpdate.Click, AddressOf BtnUpdate_Click
        buttonPanel.Controls.Add(btnUpdate)

        ' Cancel Button
        Dim btnCancel As New Button()
        btnCancel.Name = "btnBatal"
        btnCancel.Text = "BATAL"
        btnCancel.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        btnCancel.Size = New Size(90, 35)
        btnCancel.Location = New Point(190, 0)
        btnCancel.BackColor = Color.FromArgb(149, 165, 166)
        btnCancel.ForeColor = Color.White
        btnCancel.FlatStyle = FlatStyle.Flat
        btnCancel.FlatAppearance.BorderSize = 0
        btnCancel.Cursor = Cursors.Hand
        AddHandler btnCancel.Click, AddressOf BtnBatal_Click
        buttonPanel.Controls.Add(btnCancel)
    End Sub

    Private Sub CreateDataGridControls(parent As Panel)
        ' Title
        Dim lblTitle As New Label()
        lblTitle.Text = "DAFTAR KATEGORI"
        lblTitle.Font = New Font("Segoe UI", 14, FontStyle.Bold)
        lblTitle.ForeColor = Color.FromArgb(52, 73, 94)
        lblTitle.Size = New Size(300, 30)
        lblTitle.Location = New Point(10, 10)
        parent.Controls.Add(lblTitle)

        ' Search Panel
        Dim searchPanel As New Panel()
        searchPanel.Size = New Size(parent.Width - 30, 40)
        searchPanel.Location = New Point(10, 50)
        parent.Controls.Add(searchPanel)

        Dim lblSearch As New Label()
        lblSearch.Text = "Cari:"
        lblSearch.Font = New Font("Segoe UI", 10)
        lblSearch.Size = New Size(40, 25)
        lblSearch.Location = New Point(0, 8)
        searchPanel.Controls.Add(lblSearch)

        Dim txtSearch As New TextBox()
        txtSearch.Name = "txtCari"
        txtSearch.Font = New Font("Segoe UI", 10)
        txtSearch.Size = New Size(200, 25)
        txtSearch.Location = New Point(45, 5)
        txtSearch.BorderStyle = BorderStyle.FixedSingle
        AddHandler txtSearch.TextChanged, AddressOf TxtCari_TextChanged
        searchPanel.Controls.Add(txtSearch)

        Dim btnRefresh As New Button()
        btnRefresh.Name = "btnRefresh"
        btnRefresh.Text = "REFRESH"
        btnRefresh.Font = New Font("Segoe UI", 9, FontStyle.Bold)
        btnRefresh.Size = New Size(80, 25)
        btnRefresh.Location = New Point(250, 5)
        btnRefresh.BackColor = Color.FromArgb(52, 152, 219)
        btnRefresh.ForeColor = Color.White
        btnRefresh.FlatStyle = FlatStyle.Flat
        btnRefresh.FlatAppearance.BorderSize = 0
        btnRefresh.Cursor = Cursors.Hand
        AddHandler btnRefresh.Click, AddressOf BtnRefresh_Click
        searchPanel.Controls.Add(btnRefresh)

        ' DataGridView
        Dim dgv As New DataGridView()
        dgv.Name = "dgvKategori"
        dgv.Size = New Size(parent.Width - 30, parent.Height - 140)
        dgv.Location = New Point(10, 100)
        dgv.Anchor = AnchorStyles.Top Or AnchorStyles.Bottom Or AnchorStyles.Left Or AnchorStyles.Right
        parent.Controls.Add(dgv)

        ' Action Buttons Panel
        Dim actionPanel As New Panel()
        actionPanel.Size = New Size(parent.Width - 30, 40)
        actionPanel.Location = New Point(10, parent.Height - 50)
        actionPanel.Anchor = AnchorStyles.Bottom Or AnchorStyles.Left Or AnchorStyles.Right
        parent.Controls.Add(actionPanel)

        Dim btnEdit As New Button()
        btnEdit.Name = "btnEdit"
        btnEdit.Text = "EDIT"
        btnEdit.Font = New Font("Segoe UI", 9, FontStyle.Bold)
        btnEdit.Size = New Size(70, 30)
        btnEdit.Location = New Point(0, 5)
        btnEdit.BackColor = Color.FromArgb(243, 156, 18)
        btnEdit.ForeColor = Color.White
        btnEdit.FlatStyle = FlatStyle.Flat
        btnEdit.FlatAppearance.BorderSize = 0
        btnEdit.Cursor = Cursors.Hand
        AddHandler btnEdit.Click, AddressOf BtnEdit_Click
        actionPanel.Controls.Add(btnEdit)

        Dim btnDelete As New Button()
        btnDelete.Name = "btnHapus"
        btnDelete.Text = "HAPUS"
        btnDelete.Font = New Font("Segoe UI", 9, FontStyle.Bold)
        btnDelete.Size = New Size(70, 30)
        btnDelete.Location = New Point(75, 5)
        btnDelete.BackColor = Color.FromArgb(231, 76, 60)
        btnDelete.ForeColor = Color.White
        btnDelete.FlatStyle = FlatStyle.Flat
        btnDelete.FlatAppearance.BorderSize = 0
        btnDelete.Cursor = Cursors.Hand
        AddHandler btnDelete.Click, AddressOf BtnHapus_Click
        actionPanel.Controls.Add(btnDelete)
    End Sub

    Private Sub SetupDataGridView()
        Dim dgv As DataGridView = DirectCast(Me.Controls.Find("dgvKategori", True)(0), DataGridView)

        ' Set DataGridView properties
        dgv.AllowUserToAddRows = False
        dgv.AllowUserToDeleteRows = False
        dgv.ReadOnly = True
        dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgv.MultiSelect = False
        dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        dgv.BackgroundColor = Color.White
        dgv.BorderStyle = BorderStyle.None
        dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal
        dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219)
        dgv.DefaultCellStyle.SelectionForeColor = Color.White
        dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94)
        dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White
        dgv.ColumnHeadersDefaultCellStyle.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        dgv.EnableHeadersVisualStyles = False
        dgv.RowHeadersVisible = False

        ' Add double click event
        AddHandler dgv.CellDoubleClick, AddressOf DgvKategori_CellDoubleClick
    End Sub

    Private Sub LoadKategoriData()
        Try
            Dim dgv As DataGridView = DirectCast(Me.Controls.Find("dgvKategori", True)(0), DataGridView)
            Dim listKategori As List(Of Kategori) = kategoriRepo.GetAllKategori()

            ' Create DataTable
            Dim dt As New DataTable()
            dt.Columns.Add("ID", GetType(Integer))
            dt.Columns.Add("Nama Kategori", GetType(String))
            dt.Columns.Add("Deskripsi", GetType(String))
            dt.Columns.Add("Tanggal Dibuat", GetType(DateTime))

            ' Fill DataTable
            For Each kategori In listKategori
                dt.Rows.Add(kategori.IdKategori, kategori.NamaKategori, kategori.Deskripsi, kategori.TanggalDibuat)
            Next

            dgv.DataSource = dt

            ' Hide ID column
            If dgv.Columns.Count > 0 Then
                dgv.Columns("ID").Visible = False
            End If

        Catch ex As Exception
            MessageBox.Show("Error loading data: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetFormMode(editMode As Boolean)
        isEditMode = editMode

        Dim btnSimpan As Button = DirectCast(Me.Controls.Find("btnSimpan", True)(0), Button)
        Dim btnUpdate As Button = DirectCast(Me.Controls.Find("btnUpdate", True)(0), Button)

        btnSimpan.Visible = Not editMode
        btnUpdate.Visible = editMode

        If Not editMode Then
            ClearForm()
            selectedKategoriId = 0
        End If
    End Sub

    Private Sub ClearForm()
        Dim txtNama As TextBox = DirectCast(Me.Controls.Find("txtNamaKategori", True)(0), TextBox)
        Dim txtDeskripsi As TextBox = DirectCast(Me.Controls.Find("txtDeskripsi", True)(0), TextBox)

        txtNama.Clear()
        txtDeskripsi.Clear()
        txtNama.Focus()
    End Sub

    Private Sub BtnSimpan_Click(sender As Object, e As EventArgs)
        Try
            Dim txtNama As TextBox = DirectCast(Me.Controls.Find("txtNamaKategori", True)(0), TextBox)
            Dim txtDeskripsi As TextBox = DirectCast(Me.Controls.Find("txtDeskripsi", True)(0), TextBox)

            Dim kategori As New Kategori()
            kategori.NamaKategori = txtNama.Text.Trim()
            kategori.Deskripsi = txtDeskripsi.Text.Trim()

            Dim result As DatabaseResponse = kategoriRepo.TambahKategori(kategori)

            If result.Success Then
                MessageBox.Show(result.Message, "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadKategoriData()
                ClearForm()
            Else
                MessageBox.Show(result.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If

        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnUpdate_Click(sender As Object, e As EventArgs)
        Try
            Dim txtNama As TextBox = DirectCast(Me.Controls.Find("txtNamaKategori", True)(0), TextBox)
            Dim txtDeskripsi As TextBox = DirectCast(Me.Controls.Find("txtDeskripsi", True)(0), TextBox)

            Dim kategori As New Kategori()
            kategori.IdKategori = selectedKategoriId
            kategori.NamaKategori = txtNama.Text.Trim()
            kategori.Deskripsi = txtDeskripsi.Text.Trim()

            Dim result As DatabaseResponse = kategoriRepo.UpdateKategori(kategori)

            If result.Success Then
                MessageBox.Show(result.Message, "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadKategoriData()
                SetFormMode(False)
            Else
                MessageBox.Show(result.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If

        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnBatal_Click(sender As Object, e As EventArgs)
        SetFormMode(False)
    End Sub

    Private Sub BtnEdit_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = DirectCast(Me.Controls.Find("dgvKategori", True)(0), DataGridView)

        If dgv.SelectedRows.Count > 0 Then
            Dim selectedRow As DataGridViewRow = dgv.SelectedRows(0)
            selectedKategoriId = Convert.ToInt32(selectedRow.Cells("ID").Value)

            Dim txtNama As TextBox = DirectCast(Me.Controls.Find("txtNamaKategori", True)(0), TextBox)
            Dim txtDeskripsi As TextBox = DirectCast(Me.Controls.Find("txtDeskripsi", True)(0), TextBox)

            txtNama.Text = selectedRow.Cells("Nama Kategori").Value.ToString()
            txtDeskripsi.Text = If(IsDBNull(selectedRow.Cells("Deskripsi").Value), "", selectedRow.Cells("Deskripsi").Value.ToString())

            SetFormMode(True)
        Else
            MessageBox.Show("Pilih kategori yang akan diedit!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub BtnHapus_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = DirectCast(Me.Controls.Find("dgvKategori", True)(0), DataGridView)

        If dgv.SelectedRows.Count > 0 Then
            Dim selectedRow As DataGridViewRow = dgv.SelectedRows(0)
            Dim namaKategori As String = selectedRow.Cells("Nama Kategori").Value.ToString()

            If MessageBox.Show($"Apakah Anda yakin ingin menghapus kategori '{namaKategori}'?", "Konfirmasi Hapus", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                Dim id As Integer = Convert.ToInt32(selectedRow.Cells("ID").Value)
                Dim result As DatabaseResponse = kategoriRepo.HapusKategori(id)

                If result.Success Then
                    MessageBox.Show(result.Message, "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    LoadKategoriData()
                    SetFormMode(False)
                Else
                    MessageBox.Show(result.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End If
            End If
        Else
            MessageBox.Show("Pilih kategori yang akan dihapus!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        LoadKategoriData()
        SetFormMode(False)
    End Sub

    Private Sub TxtCari_TextChanged(sender As Object, e As EventArgs)
        Dim txtCari As TextBox = DirectCast(sender, TextBox)
        Dim dgv As DataGridView = DirectCast(Me.Controls.Find("dgvKategori", True)(0), DataGridView)

        If dgv.DataSource IsNot Nothing Then
            Dim dt As DataTable = DirectCast(dgv.DataSource, DataTable)
            If String.IsNullOrWhiteSpace(txtCari.Text) Then
                dt.DefaultView.RowFilter = ""
            Else
                dt.DefaultView.RowFilter = $"[Nama Kategori] LIKE '%{txtCari.Text}%' OR [Deskripsi] LIKE '%{txtCari.Text}%'"
            End If
        End If
    End Sub

    Private Sub DgvKategori_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs)
        If e.RowIndex >= 0 Then
            BtnEdit_Click(Nothing, Nothing)
        End If
    End Sub
End Class
