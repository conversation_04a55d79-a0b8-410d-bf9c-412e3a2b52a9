﻿Public Class FormUtama
    Private kategoriRepo As New KategoriRepository()
    Private produkRepo As New ProdukRepository()
    Private userRepo As New UserRepository()

    Private Sub FormUtama_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' Debug: Check CurrentUser state
            System.Diagnostics.Debug.WriteLine($"FormUtama_Load - CurrentUser ID: {CurrentUser.Instance.IdUser}, Username: {CurrentUser.Instance.Username}, Role: {CurrentUser.Instance.Role}")
            System.Diagnostics.Debug.WriteLine($"FormUtama_Load - IsLoggedIn: {CurrentUser.Instance.IsLoggedIn()}")

            ' Check if user is logged in
            If Not CurrentUser.Instance.IsLoggedIn() Then
                MessageBox.Show($"Sesi login tidak valid. Silakan login kembali.{vbCrLf}Debug: ID={CurrentUser.Instance.IdUser}, User={CurrentUser.Instance.Username}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Me.Close()
                Return
            End If

            ' Test database connection
            If Not DatabaseHelper.TestConnection() Then
                MessageBox.Show("Koneksi database terputus. Aplikasi akan ditutup.", "Error Database", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Me.Close()
                Return
            End If

            InitializeForm()
            SetupMenuStrip()
            SetupStatusStrip()
            LoadDashboardData()
            CheckUserPermissions()
        Catch ex As Exception
            MessageBox.Show("Error saat memuat form utama: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Me.Close()
        End Try
    End Sub

    Private Sub InitializeForm()
        ' Set form properties
        Me.Text = "Aplikasi Toko/Penjualan - Dashboard"
        Me.Size = New Size(1200, 800)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.WindowState = FormWindowState.Maximized
        Me.BackColor = Color.FromArgb(236, 240, 241)
        Me.IsMdiContainer = True

        ' Set icon if available
        Try
            ' Me.Icon = My.Resources.app_icon ' Icon not available yet
            ' Icon will be added later when resources are available
        Catch
            ' Icon not available, continue without it
        End Try
    End Sub

    Private Sub SetupMenuStrip()
        Dim menuStrip As New MenuStrip()
        menuStrip.BackColor = Color.FromArgb(52, 73, 94)
        menuStrip.ForeColor = Color.White
        menuStrip.Font = New Font("Segoe UI", 10, FontStyle.Bold)

        ' Menu Master Data
        Dim menuMaster As New ToolStripMenuItem("Master Data")
        menuMaster.ForeColor = Color.White

        Dim menuKategori As New ToolStripMenuItem("Kategori Produk")
        ' menuKategori.Image = My.Resources.category_icon ' Icon not available yet
        AddHandler menuKategori.Click, AddressOf MenuKategori_Click

        Dim menuProduk As New ToolStripMenuItem("Produk")
        ' menuProduk.Image = My.Resources.product_icon ' Icon not available yet
        AddHandler menuProduk.Click, AddressOf MenuProduk_Click

        menuMaster.DropDownItems.AddRange({menuKategori, menuProduk})

        ' Menu Transaksi (placeholder for future development)
        Dim menuTransaksi As New ToolStripMenuItem("Transaksi")
        menuTransaksi.ForeColor = Color.White

        Dim menuPenjualan As New ToolStripMenuItem("Penjualan")
        AddHandler menuPenjualan.Click, AddressOf MenuPenjualan_Click

        Dim menuLaporan As New ToolStripMenuItem("Laporan")
        AddHandler menuLaporan.Click, AddressOf MenuLaporan_Click

        menuTransaksi.DropDownItems.AddRange({menuPenjualan, menuLaporan})

        ' Menu Sistem
        Dim menuSistem As New ToolStripMenuItem("Sistem")
        menuSistem.ForeColor = Color.White

        Dim menuUser As New ToolStripMenuItem("Manajemen User")
        ' menuUser.Image = My.Resources.user_icon ' Icon not available yet
        AddHandler menuUser.Click, AddressOf MenuUser_Click

        Dim menuGantiPassword As New ToolStripMenuItem("Ganti Password")
        AddHandler menuGantiPassword.Click, AddressOf MenuGantiPassword_Click

        Dim menuLogout As New ToolStripMenuItem("Logout")
        ' menuLogout.Image = My.Resources.logout_icon ' Icon not available yet
        AddHandler menuLogout.Click, AddressOf MenuLogout_Click

        menuSistem.DropDownItems.AddRange({menuUser, New ToolStripSeparator(), menuGantiPassword, New ToolStripSeparator(), menuLogout})

        ' Menu Help
        Dim menuHelp As New ToolStripMenuItem("Bantuan")
        menuHelp.ForeColor = Color.White

        Dim menuAbout As New ToolStripMenuItem("Tentang Aplikasi")
        AddHandler menuAbout.Click, AddressOf MenuAbout_Click

        menuHelp.DropDownItems.Add(menuAbout)

        ' Add menus to menustrip
        menuStrip.Items.AddRange({menuMaster, menuTransaksi, menuSistem, menuHelp})

        Me.MainMenuStrip = menuStrip
        Me.Controls.Add(menuStrip)
    End Sub

    Private Sub SetupStatusStrip()
        Dim statusStrip As New StatusStrip()
        statusStrip.BackColor = Color.FromArgb(52, 73, 94)
        statusStrip.ForeColor = Color.White

        ' User info
        Dim lblUser As New ToolStripStatusLabel()
        Dim userName As String = If(String.IsNullOrEmpty(CurrentUser.Instance.NamaLengkap), "Unknown", CurrentUser.Instance.NamaLengkap)
        Dim userRole As String = If(String.IsNullOrEmpty(CurrentUser.Instance.Role), "USER", CurrentUser.Instance.Role.ToUpper())
        lblUser.Text = $"User: {userName} ({userRole})"
        lblUser.ForeColor = Color.White

        ' Login time
        Dim lblLoginTime As New ToolStripStatusLabel()
        lblLoginTime.Text = $"Login: {CurrentUser.Instance.WaktuLogin:dd/MM/yyyy HH:mm}"
        lblLoginTime.ForeColor = Color.White

        ' Current time
        Dim lblCurrentTime As New ToolStripStatusLabel()
        lblCurrentTime.Name = "lblCurrentTime"
        lblCurrentTime.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")
        lblCurrentTime.ForeColor = Color.White

        ' Spring to push items to right
        Dim spring As New ToolStripStatusLabel()
        spring.Spring = True

        statusStrip.Items.AddRange({lblUser, spring, lblLoginTime, lblCurrentTime})
        Me.Controls.Add(statusStrip)

        ' Timer for current time update
        Dim timer As New Timer()
        timer.Interval = 1000 ' Update every second
        AddHandler timer.Tick, Sub()
                                   lblCurrentTime.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")
                               End Sub
        timer.Start()
    End Sub

    Private Sub LoadDashboardData()
        Try
            ' Create dashboard panel
            Dim dashboardPanel As New Panel()
            dashboardPanel.Dock = DockStyle.Fill
            dashboardPanel.BackColor = Color.FromArgb(236, 240, 241)
            dashboardPanel.Padding = New Padding(20)
            Me.Controls.Add(dashboardPanel)
            dashboardPanel.BringToFront()

            ' Welcome label
            Dim lblWelcome As New Label()
            lblWelcome.Text = $"Selamat Datang, {CurrentUser.Instance.NamaLengkap}!"
            lblWelcome.Font = New Font("Segoe UI", 18, FontStyle.Bold)
            lblWelcome.ForeColor = Color.FromArgb(52, 73, 94)
            lblWelcome.Size = New Size(600, 40)
            lblWelcome.Location = New Point(20, 20)
            dashboardPanel.Controls.Add(lblWelcome)

            ' Statistics cards
            CreateStatisticsCards(dashboardPanel)

            ' Recent activities or alerts
            CreateAlertsPanel(dashboardPanel)

        Catch ex As Exception
            MessageBox.Show("Error loading dashboard: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub CreateStatisticsCards(parent As Panel)
        Dim startX As Integer = 20
        Dim startY As Integer = 80
        Dim cardWidth As Integer = 200
        Dim cardHeight As Integer = 120
        Dim spacing As Integer = 20

        ' Total Produk Card
        Dim totalProduk As Integer = produkRepo.GetTotalProduk()
        CreateStatCard(parent, "Total Produk", totalProduk.ToString(), Color.FromArgb(52, 152, 219), startX, startY, cardWidth, cardHeight)

        ' Total Kategori Card
        Dim totalKategori As Integer = kategoriRepo.GetTotalKategori()
        CreateStatCard(parent, "Total Kategori", totalKategori.ToString(), Color.FromArgb(46, 204, 113), startX + cardWidth + spacing, startY, cardWidth, cardHeight)

        ' Total User Card (only for admin/manager)
        If CurrentUser.Instance.IsManager() Then
            Dim totalUser As Integer = userRepo.GetTotalUser()
            CreateStatCard(parent, "Total User", totalUser.ToString(), Color.FromArgb(155, 89, 182), startX + (cardWidth + spacing) * 2, startY, cardWidth, cardHeight)
        End If

        ' Stok Menipis Card
        Dim produkStokMenipis As List(Of Produk) = produkRepo.GetProdukStokMenipis()
        CreateStatCard(parent, "Stok Menipis", produkStokMenipis.Count.ToString(), Color.FromArgb(231, 76, 60), startX + (cardWidth + spacing) * 3, startY, cardWidth, cardHeight)
    End Sub

    Private Sub CreateStatCard(parent As Panel, title As String, value As String, color As Color, x As Integer, y As Integer, width As Integer, height As Integer)
        Dim card As New Panel()
        card.Size = New Size(width, height)
        card.Location = New Point(x, y)
        card.BackColor = Color.White
        card.BorderStyle = BorderStyle.FixedSingle

        ' Header
        Dim header As New Panel()
        header.Size = New Size(width, 40)
        header.Location = New Point(0, 0)
        header.BackColor = color
        card.Controls.Add(header)

        ' Title
        Dim lblTitle As New Label()
        lblTitle.Text = title
        lblTitle.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblTitle.ForeColor = Color.White
        lblTitle.Size = New Size(width, 40)
        lblTitle.TextAlign = ContentAlignment.MiddleCenter
        header.Controls.Add(lblTitle)

        ' Value
        Dim lblValue As New Label()
        lblValue.Text = value
        lblValue.Font = New Font("Segoe UI", 24, FontStyle.Bold)
        lblValue.ForeColor = color
        lblValue.Size = New Size(width, 80)
        lblValue.Location = New Point(0, 40)
        lblValue.TextAlign = ContentAlignment.MiddleCenter
        card.Controls.Add(lblValue)

        parent.Controls.Add(card)
    End Sub

    Private Sub CreateAlertsPanel(parent As Panel)
        Try
            ' Get products with low stock
            Dim produkStokMenipis As List(Of Produk) = produkRepo.GetProdukStokMenipis()

            If produkStokMenipis.Count > 0 Then
                Dim alertPanel As New Panel()
                alertPanel.Size = New Size(parent.Width - 40, 200)
                alertPanel.Location = New Point(20, 220)
                alertPanel.BackColor = Color.White
                alertPanel.BorderStyle = BorderStyle.FixedSingle

                ' Alert header
                Dim headerPanel As New Panel()
                headerPanel.Size = New Size(alertPanel.Width, 40)
                headerPanel.Location = New Point(0, 0)
                headerPanel.BackColor = Color.FromArgb(231, 76, 60)
                alertPanel.Controls.Add(headerPanel)

                Dim lblAlertTitle As New Label()
                lblAlertTitle.Text = "⚠️ PERINGATAN STOK MENIPIS"
                lblAlertTitle.Font = New Font("Segoe UI", 12, FontStyle.Bold)
                lblAlertTitle.ForeColor = Color.White
                lblAlertTitle.Size = New Size(headerPanel.Width, 40)
                lblAlertTitle.TextAlign = ContentAlignment.MiddleCenter
                headerPanel.Controls.Add(lblAlertTitle)

                ' Alert content
                Dim contentPanel As New Panel()
                contentPanel.Size = New Size(alertPanel.Width, 160)
                contentPanel.Location = New Point(0, 40)
                contentPanel.BackColor = Color.White
                contentPanel.AutoScroll = True
                alertPanel.Controls.Add(contentPanel)

                Dim yPos As Integer = 10
                For Each produk In produkStokMenipis.Take(5) ' Show only first 5
                    Dim lblAlert As New Label()
                    lblAlert.Text = $"• {produk.NamaProduk} - Stok: {produk.Stok} (Min: {produk.StokMinimum})"
                    lblAlert.Font = New Font("Segoe UI", 9)
                    lblAlert.ForeColor = Color.FromArgb(52, 73, 94)
                    lblAlert.Size = New Size(contentPanel.Width - 20, 20)
                    lblAlert.Location = New Point(10, yPos)
                    contentPanel.Controls.Add(lblAlert)
                    yPos += 25
                Next

                If produkStokMenipis.Count > 5 Then
                    Dim lblMore As New Label()
                    lblMore.Text = $"... dan {produkStokMenipis.Count - 5} produk lainnya"
                    lblMore.Font = New Font("Segoe UI", 9, FontStyle.Italic)
                    lblMore.ForeColor = Color.FromArgb(127, 140, 141)
                    lblMore.Size = New Size(contentPanel.Width - 20, 20)
                    lblMore.Location = New Point(10, yPos)
                    contentPanel.Controls.Add(lblMore)
                End If

                parent.Controls.Add(alertPanel)
            End If

        Catch ex As Exception
            ' Handle error silently for dashboard
        End Try
    End Sub

    Private Sub CheckUserPermissions()
        ' Hide/show menu items based on user role
        Dim menuStrip As MenuStrip = DirectCast(Me.Controls.OfType(Of MenuStrip)().First(), MenuStrip)

        ' User management only for admin/manager
        If Not CurrentUser.Instance.IsManager() Then
            Dim sistemMenu As ToolStripMenuItem = DirectCast(menuStrip.Items("Sistem"), ToolStripMenuItem)
            If sistemMenu IsNot Nothing Then
                Dim userMenu As ToolStripMenuItem = DirectCast(sistemMenu.DropDownItems("Manajemen User"), ToolStripMenuItem)
                If userMenu IsNot Nothing Then
                    userMenu.Visible = False
                End If
            End If
        End If
    End Sub

    ' Menu Event Handlers
    Private Sub MenuKategori_Click(sender As Object, e As EventArgs)
        OpenChildForm(New FormKategori())
    End Sub

    Private Sub MenuProduk_Click(sender As Object, e As EventArgs)
        OpenChildForm(New FormProduk())
    End Sub

    Private Sub MenuUser_Click(sender As Object, e As EventArgs)
        If CurrentUser.Instance.IsManager() Then
            OpenChildForm(New FormUser())
        Else
            MessageBox.Show("Anda tidak memiliki akses ke menu ini!", "Akses Ditolak", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub MenuPenjualan_Click(sender As Object, e As EventArgs)
        MessageBox.Show("Fitur Penjualan akan segera hadir!", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MenuLaporan_Click(sender As Object, e As EventArgs)
        MessageBox.Show("Fitur Laporan akan segera hadir!", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MenuGantiPassword_Click(sender As Object, e As EventArgs)
        MessageBox.Show("Fitur Ganti Password akan segera hadir!" & vbCrLf &
                       "Untuk sementara, gunakan fitur reset password di menu User Management.",
                       "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub MenuLogout_Click(sender As Object, e As EventArgs)
        If MessageBox.Show("Apakah Anda yakin ingin logout?", "Konfirmasi Logout", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            userRepo.Logout()
            Me.Hide()
            Dim loginForm As New FormLogin()
            loginForm.Show()
            Me.Close()
        End If
    End Sub

    Private Sub MenuAbout_Click(sender As Object, e As EventArgs)
        MessageBox.Show("Aplikasi Toko/Penjualan v1.0" & vbCrLf &
                       "Dibuat dengan VB.NET dan MySQL" & vbCrLf &
                       "© 2024 - Sistem Manajemen Toko Modern",
                       "Tentang Aplikasi", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub OpenChildForm(childForm As Form)
        ' Close existing child forms of the same type
        For Each form As Form In Me.MdiChildren
            If form.GetType() = childForm.GetType() Then
                form.Close()
            End If
        Next

        ' Open new child form
        childForm.MdiParent = Me
        childForm.Show()
    End Sub

    Private Sub FormUtama_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        If MessageBox.Show("Apakah Anda yakin ingin keluar dari aplikasi?", "Konfirmasi", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
            e.Cancel = True
        End If
    End Sub
End Class
