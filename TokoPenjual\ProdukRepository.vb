﻿Imports MySql.Data.MySqlClient

Public Class ProdukRepository

    ' Mendapatkan semua produk dengan informasi kategori
    Public Function GetAllProduk() As List(Of Produk)
        Dim listProduk As New List(Of Produk)
        Dim query As String = "SELECT p.id_produk, p.kode_produk, p.nama_produk, p.deskripsi, p.harga_beli, p.harga_jual, " &
                              "p.stok, p.stok_minimum, p.id_kategori, k.nama_kategori, p.tanggal_dibuat, p.tanggal_diubah, p.status_aktif " &
                              "FROM tb_produk p INNER JOIN tb_kategori k ON p.id_kategori = k.id_kategori " &
                              "WHERE p.status_aktif = 1 ORDER BY p.nama_produk"

        Try
            Dim dt As DataTable = DatabaseHelper.ExecuteQuery(query)
            For Each row As DataRow In dt.Rows
                listProduk.Add(CreateProdukFromDataRow(row))
            Next
        Catch ex As Exception
            MessageBox.Show("Error saat mengambil data produk: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Return listProduk
    End Function

    ' Mendapatkan produk berdasarkan ID
    Public Function GetProdukById(id As Integer) As Produk
        Dim produk As New Produk()
        Dim query As String = "SELECT p.id_produk, p.kode_produk, p.nama_produk, p.deskripsi, p.harga_beli, p.harga_jual, " &
                              "p.stok, p.stok_minimum, p.id_kategori, k.nama_kategori, p.tanggal_dibuat, p.tanggal_diubah, p.status_aktif " &
                              "FROM tb_produk p INNER JOIN tb_kategori k ON p.id_kategori = k.id_kategori " &
                              "WHERE p.id_produk = @id AND p.status_aktif = 1"
        Dim parameters As New Dictionary(Of String, Object) From {{"@id", id}}

        Try
            Dim dt As DataTable = DatabaseHelper.ExecuteQuery(query, parameters)
            If dt.Rows.Count > 0 Then
                produk = CreateProdukFromDataRow(dt.Rows(0))
            End If
        Catch ex As Exception
            MessageBox.Show("Error saat mengambil data produk: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Return produk
    End Function

    ' Pencarian produk dengan filter
    Public Function CariProduk(keyword As String, Optional idKategori As Integer = 0, Optional statusStok As String = "") As List(Of Produk)
        Dim listProduk As New List(Of Produk)
        Dim query As String = "SELECT p.id_produk, p.kode_produk, p.nama_produk, p.deskripsi, p.harga_beli, p.harga_jual, " &
                              "p.stok, p.stok_minimum, p.id_kategori, k.nama_kategori, p.tanggal_dibuat, p.tanggal_diubah, p.status_aktif " &
                              "FROM tb_produk p INNER JOIN tb_kategori k ON p.id_kategori = k.id_kategori " &
                              "WHERE p.status_aktif = 1"

        Dim parameters As New Dictionary(Of String, Object)

        ' Filter berdasarkan keyword
        If Not String.IsNullOrWhiteSpace(keyword) Then
            query &= " AND (p.nama_produk LIKE @keyword OR p.kode_produk LIKE @keyword)"
            parameters.Add("@keyword", "%" & keyword.Trim() & "%")
        End If

        ' Filter berdasarkan kategori
        If idKategori > 0 Then
            query &= " AND p.id_kategori = @idKategori"
            parameters.Add("@idKategori", idKategori)
        End If

        ' Filter berdasarkan status stok
        If Not String.IsNullOrWhiteSpace(statusStok) Then
            Select Case statusStok.ToLower()
                Case "stok habis"
                    query &= " AND p.stok = 0"
                Case "stok menipis"
                    query &= " AND p.stok > 0 AND p.stok <= p.stok_minimum"
                Case "stok aman"
                    query &= " AND p.stok > p.stok_minimum"
            End Select
        End If

        query &= " ORDER BY p.nama_produk"

        Try
            Dim dt As DataTable = DatabaseHelper.ExecuteQuery(query, parameters)
            For Each row As DataRow In dt.Rows
                listProduk.Add(CreateProdukFromDataRow(row))
            Next
        Catch ex As Exception
            MessageBox.Show("Error saat mencari produk: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Return listProduk
    End Function

    ' Menambah produk baru
    Public Function TambahProduk(produk As Produk) As DatabaseResponse
        Dim response As New DatabaseResponse()

        ' Validasi input
        Dim validationResult = ValidateProduk(produk)
        If Not validationResult.Success Then
            Return validationResult
        End If

        ' Cek apakah kode produk sudah ada
        If IsKodeProdukExists(produk.KodeProduk) Then
            response.Message = "Kode produk sudah ada!"
            Return response
        End If

        Dim query As String = "INSERT INTO tb_produk (kode_produk, nama_produk, deskripsi, harga_beli, harga_jual, stok, stok_minimum, id_kategori) " &
                              "VALUES (@kode, @nama, @deskripsi, @hargaBeli, @hargaJual, @stok, @stokMin, @idKategori)"

        Dim parameters As New Dictionary(Of String, Object) From {
            {"@kode", produk.KodeProduk.Trim().ToUpper()},
            {"@nama", produk.NamaProduk.Trim()},
            {"@deskripsi", If(String.IsNullOrWhiteSpace(produk.Deskripsi), "", produk.Deskripsi.Trim())},
            {"@hargaBeli", produk.HargaBeli},
            {"@hargaJual", produk.HargaJual},
            {"@stok", produk.Stok},
            {"@stokMin", produk.StokMinimum},
            {"@idKategori", produk.IdKategori}
        }

        Try
            If DatabaseHelper.ExecuteNonQuery(query, parameters) Then
                response.Success = True
                response.Message = "Produk berhasil ditambahkan!"
                response.Data = DatabaseHelper.GetLastInsertId()
            Else
                response.Message = "Gagal menambahkan produk!"
            End If
        Catch ex As Exception
            response.Message = "Error: " & ex.Message
        End Try

        Return response
    End Function

    ' Mengupdate produk
    Public Function UpdateProduk(produk As Produk) As DatabaseResponse
        Dim response As New DatabaseResponse()

        ' Validasi input
        If produk.IdProduk <= 0 Then
            response.Message = "ID produk tidak valid!"
            Return response
        End If

        Dim validationResult = ValidateProduk(produk)
        If Not validationResult.Success Then
            Return validationResult
        End If

        ' Cek apakah kode produk sudah ada (kecuali untuk produk yang sedang diedit)
        If IsKodeProdukExists(produk.KodeProduk, produk.IdProduk) Then
            response.Message = "Kode produk sudah ada!"
            Return response
        End If

        Dim query As String = "UPDATE tb_produk SET kode_produk = @kode, nama_produk = @nama, deskripsi = @deskripsi, " &
                              "harga_beli = @hargaBeli, harga_jual = @hargaJual, stok = @stok, stok_minimum = @stokMin, " &
                              "id_kategori = @idKategori WHERE id_produk = @id"

        Dim parameters As New Dictionary(Of String, Object) From {
            {"@kode", produk.KodeProduk.Trim().ToUpper()},
            {"@nama", produk.NamaProduk.Trim()},
            {"@deskripsi", If(String.IsNullOrWhiteSpace(produk.Deskripsi), "", produk.Deskripsi.Trim())},
            {"@hargaBeli", produk.HargaBeli},
            {"@hargaJual", produk.HargaJual},
            {"@stok", produk.Stok},
            {"@stokMin", produk.StokMinimum},
            {"@idKategori", produk.IdKategori},
            {"@id", produk.IdProduk}
        }

        Try
            If DatabaseHelper.ExecuteNonQuery(query, parameters) Then
                response.Success = True
                response.Message = "Produk berhasil diupdate!"
            Else
                response.Message = "Gagal mengupdate produk!"
            End If
        Catch ex As Exception
            response.Message = "Error: " & ex.Message
        End Try

        Return response
    End Function

    ' Menghapus produk (soft delete)
    Public Function HapusProduk(id As Integer) As DatabaseResponse
        Dim response As New DatabaseResponse()

        If id <= 0 Then
            response.Message = "ID produk tidak valid!"
            Return response
        End If

        Dim query As String = "UPDATE tb_produk SET status_aktif = 0 WHERE id_produk = @id"
        Dim parameters As New Dictionary(Of String, Object) From {{"@id", id}}

        Try
            If DatabaseHelper.ExecuteNonQuery(query, parameters) Then
                response.Success = True
                response.Message = "Produk berhasil dihapus!"
            Else
                response.Message = "Gagal menghapus produk!"
            End If
        Catch ex As Exception
            response.Message = "Error: " & ex.Message
        End Try

        Return response
    End Function

    ' Helper method untuk membuat objek Produk dari DataRow
    Private Function CreateProdukFromDataRow(row As DataRow) As Produk
        Dim produk As New Produk()
        produk.IdProduk = Convert.ToInt32(row("id_produk"))
        produk.KodeProduk = row("kode_produk").ToString()
        produk.NamaProduk = row("nama_produk").ToString()
        produk.Deskripsi = If(IsDBNull(row("deskripsi")), "", row("deskripsi").ToString())
        produk.HargaBeli = Convert.ToDecimal(row("harga_beli"))
        produk.HargaJual = Convert.ToDecimal(row("harga_jual"))
        produk.Stok = Convert.ToInt32(row("stok"))
        produk.StokMinimum = Convert.ToInt32(row("stok_minimum"))
        produk.IdKategori = Convert.ToInt32(row("id_kategori"))
        produk.NamaKategori = row("nama_kategori").ToString()
        produk.TanggalDibuat = Convert.ToDateTime(row("tanggal_dibuat"))
        produk.TanggalDiubah = Convert.ToDateTime(row("tanggal_diubah"))
        produk.StatusAktif = Convert.ToBoolean(row("status_aktif"))
        Return produk
    End Function

    ' Validasi data produk
    Private Function ValidateProduk(produk As Produk) As DatabaseResponse
        Dim response As New DatabaseResponse()

        If String.IsNullOrWhiteSpace(produk.KodeProduk) Then
            response.Message = "Kode produk tidak boleh kosong!"
            Return response
        End If

        If String.IsNullOrWhiteSpace(produk.NamaProduk) Then
            response.Message = "Nama produk tidak boleh kosong!"
            Return response
        End If

        If produk.HargaJual <= 0 Then
            response.Message = "Harga jual harus lebih besar dari 0!"
            Return response
        End If

        If produk.HargaBeli < 0 Then
            response.Message = "Harga beli tidak boleh negatif!"
            Return response
        End If

        If produk.Stok < 0 Then
            response.Message = "Stok tidak boleh negatif!"
            Return response
        End If

        If produk.StokMinimum < 0 Then
            response.Message = "Stok minimum tidak boleh negatif!"
            Return response
        End If

        If produk.IdKategori <= 0 Then
            response.Message = "Kategori harus dipilih!"
            Return response
        End If

        response.Success = True
        Return response
    End Function

    ' Cek apakah kode produk sudah ada
    Private Function IsKodeProdukExists(kodeProduk As String, Optional excludeId As Integer = 0) As Boolean
        Dim query As String = "SELECT COUNT(*) FROM tb_produk WHERE kode_produk = @kode AND status_aktif = 1"
        Dim parameters As New Dictionary(Of String, Object) From {{"@kode", kodeProduk.Trim().ToUpper()}}

        If excludeId > 0 Then
            query &= " AND id_produk != @excludeId"
            parameters.Add("@excludeId", excludeId)
        End If

        Try
            Dim count As Integer = Convert.ToInt32(DatabaseHelper.ExecuteScalar(query, parameters))
            Return count > 0
        Catch ex As Exception
            Return False
        End Try
    End Function

    ' Mendapatkan total produk
    Public Function GetTotalProduk() As Integer
        Dim query As String = "SELECT COUNT(*) FROM tb_produk WHERE status_aktif = 1"
        Try
            Return Convert.ToInt32(DatabaseHelper.ExecuteScalar(query))
        Catch ex As Exception
            Return 0
        End Try
    End Function

    ' Mendapatkan produk dengan stok menipis
    Public Function GetProdukStokMenipis() As List(Of Produk)
        Dim listProduk As New List(Of Produk)
        Dim query As String = "SELECT p.id_produk, p.kode_produk, p.nama_produk, p.deskripsi, p.harga_beli, p.harga_jual, " &
                              "p.stok, p.stok_minimum, p.id_kategori, k.nama_kategori, p.tanggal_dibuat, p.tanggal_diubah, p.status_aktif " &
                              "FROM tb_produk p INNER JOIN tb_kategori k ON p.id_kategori = k.id_kategori " &
                              "WHERE p.status_aktif = 1 AND p.stok <= p.stok_minimum ORDER BY p.stok ASC"

        Try
            Dim dt As DataTable = DatabaseHelper.ExecuteQuery(query)
            For Each row As DataRow In dt.Rows
                listProduk.Add(CreateProdukFromDataRow(row))
            Next
        Catch ex As Exception
            MessageBox.Show("Error saat mengambil produk stok menipis: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Return listProduk
    End Function
End Class
