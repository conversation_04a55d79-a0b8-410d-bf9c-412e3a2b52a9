﻿Imports MySql.Data.MySqlClient
Imports System.Security.Cryptography
Imports System.Text

Public Class UserRepository

    ' Login user
    Public Function Login(username As String, password As String) As DatabaseResponse
        Dim response As New DatabaseResponse()

        ' Validasi input
        If String.IsNullOrWhiteSpace(username) OrElse String.IsNullOrWhiteSpace(password) Then
            response.Message = "Username dan password tidak boleh kosong!"
            Return response
        End If

        Dim query As String = "SELECT id_user, username, password, nama_lengkap, email, role, status_aktif " &
                              "FROM tb_user WHERE username = @username AND status_aktif = 1"
        Dim parameters As New Dictionary(Of String, Object) From {{"@username", username.Trim()}}

        Try
            Dim dt As DataTable = DatabaseHelper.ExecuteQuery(query, parameters)
            If dt.Rows.Count > 0 Then
                Dim row As DataRow = dt.Rows(0)
                Dim storedPassword As String = row("password").ToString()

                ' Verifikasi password (untuk demo menggunakan plain text, di production gunakan hash)
                If password = storedPassword Then
                    ' Buat objek user
                    Dim user As New User()
                    user.IdUser = Convert.ToInt32(row("id_user"))
                    user.Username = row("username").ToString()
                    user.NamaLengkap = row("nama_lengkap").ToString()
                    user.Email = If(IsDBNull(row("email")), "", row("email").ToString())
                    user.Role = row("role").ToString()
                    user.StatusAktif = Convert.ToBoolean(row("status_aktif"))

                    ' Set current user
                    CurrentUser.Instance.SetUser(user)

                    ' Update waktu login terakhir
                    UpdateLastLogin(user.IdUser)

                    response.Success = True
                    response.Message = "Login berhasil!"
                    response.Data = user
                Else
                    response.Message = "Username atau password salah!"
                End If
            Else
                response.Message = "Username atau password salah!"
            End If
        Catch ex As Exception
            response.Message = "Error saat login: " & ex.Message
        End Try

        Return response
    End Function

    ' Logout user
    Public Function Logout() As DatabaseResponse
        Dim response As New DatabaseResponse()
        Try
            CurrentUser.Instance.ClearUser()
            response.Success = True
            response.Message = "Logout berhasil!"
        Catch ex As Exception
            response.Message = "Error saat logout: " & ex.Message
        End Try
        Return response
    End Function

    ' Mendapatkan semua user
    Public Function GetAllUser() As List(Of User)
        Dim listUser As New List(Of User)
        Dim query As String = "SELECT id_user, username, nama_lengkap, email, role, tanggal_dibuat, tanggal_login_terakhir, status_aktif " &
                              "FROM tb_user WHERE status_aktif = 1 ORDER BY nama_lengkap"

        Try
            Dim dt As DataTable = DatabaseHelper.ExecuteQuery(query)
            For Each row As DataRow In dt.Rows
                Dim user As New User()
                user.IdUser = Convert.ToInt32(row("id_user"))
                user.Username = row("username").ToString()
                user.NamaLengkap = row("nama_lengkap").ToString()
                user.Email = If(IsDBNull(row("email")), "", row("email").ToString())
                user.Role = row("role").ToString()
                user.TanggalDibuat = Convert.ToDateTime(row("tanggal_dibuat"))
                user.TanggalLoginTerakhir = If(IsDBNull(row("tanggal_login_terakhir")), Nothing, Convert.ToDateTime(row("tanggal_login_terakhir")))
                user.StatusAktif = Convert.ToBoolean(row("status_aktif"))
                listUser.Add(user)
            Next
        Catch ex As Exception
            MessageBox.Show("Error saat mengambil data user: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Return listUser
    End Function

    ' Mendapatkan user berdasarkan ID
    Public Function GetUserById(id As Integer) As User
        Dim user As New User()
        Dim query As String = "SELECT id_user, username, nama_lengkap, email, role, tanggal_dibuat, tanggal_login_terakhir, status_aktif " &
                              "FROM tb_user WHERE id_user = @id AND status_aktif = 1"
        Dim parameters As New Dictionary(Of String, Object) From {{"@id", id}}

        Try
            Dim dt As DataTable = DatabaseHelper.ExecuteQuery(query, parameters)
            If dt.Rows.Count > 0 Then
                Dim row As DataRow = dt.Rows(0)
                user.IdUser = Convert.ToInt32(row("id_user"))
                user.Username = row("username").ToString()
                user.NamaLengkap = row("nama_lengkap").ToString()
                user.Email = If(IsDBNull(row("email")), "", row("email").ToString())
                user.Role = row("role").ToString()
                user.TanggalDibuat = Convert.ToDateTime(row("tanggal_dibuat"))
                user.TanggalLoginTerakhir = If(IsDBNull(row("tanggal_login_terakhir")), Nothing, Convert.ToDateTime(row("tanggal_login_terakhir")))
                user.StatusAktif = Convert.ToBoolean(row("status_aktif"))
            End If
        Catch ex As Exception
            MessageBox.Show("Error saat mengambil data user: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Return user
    End Function

    ' Menambah user baru
    Public Function TambahUser(user As User) As DatabaseResponse
        Dim response As New DatabaseResponse()

        ' Validasi input
        Dim validationResult = ValidateUser(user)
        If Not validationResult.Success Then
            Return validationResult
        End If

        ' Cek apakah username sudah ada
        If IsUsernameExists(user.Username) Then
            response.Message = "Username sudah ada!"
            Return response
        End If

        ' Cek apakah email sudah ada (jika diisi)
        If Not String.IsNullOrWhiteSpace(user.Email) AndAlso IsEmailExists(user.Email) Then
            response.Message = "Email sudah digunakan!"
            Return response
        End If

        Dim query As String = "INSERT INTO tb_user (username, password, nama_lengkap, email, role) " &
                              "VALUES (@username, @password, @namaLengkap, @email, @role)"

        Dim parameters As New Dictionary(Of String, Object) From {
            {"@username", user.Username.Trim().ToLower()},
            {"@password", user.Password}, ' Dalam production, hash password terlebih dahulu
            {"@namaLengkap", user.NamaLengkap.Trim()},
            {"@email", If(String.IsNullOrWhiteSpace(user.Email), DBNull.Value, user.Email.Trim().ToLower())},
            {"@role", user.Role.ToLower()}
        }

        Try
            If DatabaseHelper.ExecuteNonQuery(query, parameters) Then
                response.Success = True
                response.Message = "User berhasil ditambahkan!"
                response.Data = DatabaseHelper.GetLastInsertId()
            Else
                response.Message = "Gagal menambahkan user!"
            End If
        Catch ex As Exception
            response.Message = "Error: " & ex.Message
        End Try

        Return response
    End Function

    ' Mengupdate user
    Public Function UpdateUser(user As User) As DatabaseResponse
        Dim response As New DatabaseResponse()

        ' Validasi input
        If user.IdUser <= 0 Then
            response.Message = "ID user tidak valid!"
            Return response
        End If

        Dim validationResult = ValidateUser(user, False) ' False = tidak validasi password untuk update
        If Not validationResult.Success Then
            Return validationResult
        End If

        ' Cek apakah username sudah ada (kecuali untuk user yang sedang diedit)
        If IsUsernameExists(user.Username, user.IdUser) Then
            response.Message = "Username sudah ada!"
            Return response
        End If

        ' Cek apakah email sudah ada (jika diisi)
        If Not String.IsNullOrWhiteSpace(user.Email) AndAlso IsEmailExists(user.Email, user.IdUser) Then
            response.Message = "Email sudah digunakan!"
            Return response
        End If

        Dim query As String = "UPDATE tb_user SET username = @username, nama_lengkap = @namaLengkap, email = @email, role = @role WHERE id_user = @id"

        Dim parameters As New Dictionary(Of String, Object) From {
            {"@username", user.Username.Trim().ToLower()},
            {"@namaLengkap", user.NamaLengkap.Trim()},
            {"@email", If(String.IsNullOrWhiteSpace(user.Email), DBNull.Value, user.Email.Trim().ToLower())},
            {"@role", user.Role.ToLower()},
            {"@id", user.IdUser}
        }

        Try
            If DatabaseHelper.ExecuteNonQuery(query, parameters) Then
                response.Success = True
                response.Message = "User berhasil diupdate!"
            Else
                response.Message = "Gagal mengupdate user!"
            End If
        Catch ex As Exception
            response.Message = "Error: " & ex.Message
        End Try

        Return response
    End Function

    ' Mengubah password user
    Public Function ChangePassword(idUser As Integer, passwordLama As String, passwordBaru As String) As DatabaseResponse
        Dim response As New DatabaseResponse()

        ' Validasi input
        If String.IsNullOrWhiteSpace(passwordLama) OrElse String.IsNullOrWhiteSpace(passwordBaru) Then
            response.Message = "Password lama dan password baru tidak boleh kosong!"
            Return response
        End If

        If passwordBaru.Length < 6 Then
            response.Message = "Password baru minimal 6 karakter!"
            Return response
        End If

        ' Verifikasi password lama
        Dim queryCheck As String = "SELECT password FROM tb_user WHERE id_user = @id AND status_aktif = 1"
        Dim parametersCheck As New Dictionary(Of String, Object) From {{"@id", idUser}}

        Try
            Dim storedPassword As Object = DatabaseHelper.ExecuteScalar(queryCheck, parametersCheck)
            If storedPassword Is Nothing OrElse storedPassword.ToString() <> passwordLama Then
                response.Message = "Password lama tidak sesuai!"
                Return response
            End If

            ' Update password
            Dim queryUpdate As String = "UPDATE tb_user SET password = @passwordBaru WHERE id_user = @id"
            Dim parametersUpdate As New Dictionary(Of String, Object) From {
                {"@passwordBaru", passwordBaru}, ' Dalam production, hash password terlebih dahulu
                {"@id", idUser}
            }

            If DatabaseHelper.ExecuteNonQuery(queryUpdate, parametersUpdate) Then
                response.Success = True
                response.Message = "Password berhasil diubah!"
            Else
                response.Message = "Gagal mengubah password!"
            End If

        Catch ex As Exception
            response.Message = "Error: " & ex.Message
        End Try

        Return response
    End Function

    ' Menghapus user (soft delete)
    Public Function HapusUser(id As Integer) As DatabaseResponse
        Dim response As New DatabaseResponse()

        If id <= 0 Then
            response.Message = "ID user tidak valid!"
            Return response
        End If

        ' Tidak boleh menghapus user yang sedang login
        If CurrentUser.Instance.IdUser = id Then
            response.Message = "Tidak dapat menghapus user yang sedang login!"
            Return response
        End If

        Dim query As String = "UPDATE tb_user SET status_aktif = 0 WHERE id_user = @id"
        Dim parameters As New Dictionary(Of String, Object) From {{"@id", id}}

        Try
            If DatabaseHelper.ExecuteNonQuery(query, parameters) Then
                response.Success = True
                response.Message = "User berhasil dihapus!"
            Else
                response.Message = "Gagal menghapus user!"
            End If
        Catch ex As Exception
            response.Message = "Error: " & ex.Message
        End Try

        Return response
    End Function

    ' Update waktu login terakhir
    Private Sub UpdateLastLogin(idUser As Integer)
        Dim query As String = "UPDATE tb_user SET tanggal_login_terakhir = NOW() WHERE id_user = @id"
        Dim parameters As New Dictionary(Of String, Object) From {{"@id", idUser}}
        DatabaseHelper.ExecuteNonQuery(query, parameters)
    End Sub

    ' Validasi data user
    Private Function ValidateUser(user As User, Optional validatePassword As Boolean = True) As DatabaseResponse
        Dim response As New DatabaseResponse()

        If String.IsNullOrWhiteSpace(user.Username) Then
            response.Message = "Username tidak boleh kosong!"
            Return response
        End If

        If user.Username.Length < 3 Then
            response.Message = "Username minimal 3 karakter!"
            Return response
        End If

        If validatePassword AndAlso String.IsNullOrWhiteSpace(user.Password) Then
            response.Message = "Password tidak boleh kosong!"
            Return response
        End If

        If validatePassword AndAlso user.Password.Length < 6 Then
            response.Message = "Password minimal 6 karakter!"
            Return response
        End If

        If String.IsNullOrWhiteSpace(user.NamaLengkap) Then
            response.Message = "Nama lengkap tidak boleh kosong!"
            Return response
        End If

        If Not String.IsNullOrWhiteSpace(user.Email) AndAlso Not IsValidEmail(user.Email) Then
            response.Message = "Format email tidak valid!"
            Return response
        End If

        If Not {"admin", "manager", "kasir"}.Contains(user.Role.ToLower()) Then
            response.Message = "Role tidak valid!"
            Return response
        End If

        response.Success = True
        Return response
    End Function

    ' Cek apakah username sudah ada
    Private Function IsUsernameExists(username As String, Optional excludeId As Integer = 0) As Boolean
        Dim query As String = "SELECT COUNT(*) FROM tb_user WHERE username = @username AND status_aktif = 1"
        Dim parameters As New Dictionary(Of String, Object) From {{"@username", username.Trim().ToLower()}}

        If excludeId > 0 Then
            query &= " AND id_user != @excludeId"
            parameters.Add("@excludeId", excludeId)
        End If

        Try
            Dim count As Integer = Convert.ToInt32(DatabaseHelper.ExecuteScalar(query, parameters))
            Return count > 0
        Catch ex As Exception
            Return False
        End Try
    End Function

    ' Cek apakah email sudah ada
    Private Function IsEmailExists(email As String, Optional excludeId As Integer = 0) As Boolean
        Dim query As String = "SELECT COUNT(*) FROM tb_user WHERE email = @email AND status_aktif = 1"
        Dim parameters As New Dictionary(Of String, Object) From {{"@email", email.Trim().ToLower()}}

        If excludeId > 0 Then
            query &= " AND id_user != @excludeId"
            parameters.Add("@excludeId", excludeId)
        End If

        Try
            Dim count As Integer = Convert.ToInt32(DatabaseHelper.ExecuteScalar(query, parameters))
            Return count > 0
        Catch ex As Exception
            Return False
        End Try
    End Function

    ' Validasi format email
    Private Function IsValidEmail(email As String) As Boolean
        Try
            Dim addr As New System.Net.Mail.MailAddress(email)
            Return addr.Address = email
        Catch
            Return False
        End Try
    End Function

    ' Mendapatkan total user
    Public Function GetTotalUser() As Integer
        Dim query As String = "SELECT COUNT(*) FROM tb_user WHERE status_aktif = 1"
        Try
            Return Convert.ToInt32(DatabaseHelper.ExecuteScalar(query))
        Catch ex As Exception
            Return 0
        End Try
    End Function
End Class
