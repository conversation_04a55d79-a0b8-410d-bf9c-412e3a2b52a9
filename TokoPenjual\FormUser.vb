﻿Public Class FormUser
    Private userRepo As New UserRepository()
    Private isEditMode As Boolean = False
    Private selectedUserId As Integer = 0

    Private Sub FormUser_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' Check permission
        If Not CurrentUser.Instance.IsManager() Then
            MessageBox.Show("Anda tidak memiliki akses ke form ini!", "<PERSON>ks<PERSON>", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Me.Close()
            Return
        End If

        InitializeForm()
        SetupDataGridView()
        LoadUserData()
        SetFormMode(False)
    End Sub

    Private Sub InitializeForm()
        ' Set form properties
        Me.Text = "Manajemen User"
        Me.Size = New Size(900, 600)
        Me.StartPosition = FormStartPosition.CenterParent
        Me.BackColor = Color.FromArgb(236, 240, 241)
        Me.MinimumSize = New Size(800, 500)

        CreateControls()
    End Sub

    Private Sub CreateControls()
        ' Main Panel
        Dim mainPanel As New TableLayoutPanel()
        mainPanel.Dock = DockStyle.Fill
        mainPanel.ColumnCount = 2
        mainPanel.RowCount = 1
        mainPanel.ColumnStyles.Add(New ColumnStyle(SizeType.Percent, 40))
        mainPanel.ColumnStyles.Add(New ColumnStyle(SizeType.Percent, 60))
        mainPanel.Padding = New Padding(10)
        Me.Controls.Add(mainPanel)

        ' Left Panel - Form Input
        Dim leftPanel As New Panel()
        leftPanel.BackColor = Color.White
        leftPanel.BorderStyle = BorderStyle.FixedSingle
        leftPanel.Padding = New Padding(15)
        mainPanel.Controls.Add(leftPanel, 0, 0)

        ' Right Panel - Data Grid
        Dim rightPanel As New Panel()
        rightPanel.BackColor = Color.White
        rightPanel.BorderStyle = BorderStyle.FixedSingle
        rightPanel.Padding = New Padding(15)
        mainPanel.Controls.Add(rightPanel, 1, 0)

        ' Create Left Panel Controls
        CreateInputControls(leftPanel)

        ' Create Right Panel Controls
        CreateDataGridControls(rightPanel)
    End Sub

    Private Sub CreateInputControls(parent As Panel)
        Dim yPos As Integer = 10

        ' Title
        Dim lblTitle As New Label()
        lblTitle.Text = "FORM USER"
        lblTitle.Font = New Font("Segoe UI", 14, FontStyle.Bold)
        lblTitle.ForeColor = Color.FromArgb(52, 73, 94)
        lblTitle.Size = New Size(300, 30)
        lblTitle.Location = New Point(10, yPos)
        parent.Controls.Add(lblTitle)
        yPos += 50

        ' Username
        Dim lblUsername As New Label()
        lblUsername.Text = "Username:"
        lblUsername.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblUsername.ForeColor = Color.FromArgb(52, 73, 94)
        lblUsername.Size = New Size(120, 25)
        lblUsername.Location = New Point(10, yPos)
        parent.Controls.Add(lblUsername)
        yPos += 25

        Dim txtUsername As New TextBox()
        txtUsername.Name = "txtUsername"
        txtUsername.Font = New Font("Segoe UI", 10)
        txtUsername.Size = New Size(280, 25)
        txtUsername.Location = New Point(10, yPos)
        txtUsername.BorderStyle = BorderStyle.FixedSingle
        txtUsername.CharacterCasing = CharacterCasing.Lower
        parent.Controls.Add(txtUsername)
        yPos += 35

        ' Password
        Dim lblPassword As New Label()
        lblPassword.Text = "Password:"
        lblPassword.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblPassword.ForeColor = Color.FromArgb(52, 73, 94)
        lblPassword.Size = New Size(120, 25)
        lblPassword.Location = New Point(10, yPos)
        parent.Controls.Add(lblPassword)
        yPos += 25

        Dim txtPassword As New TextBox()
        txtPassword.Name = "txtPassword"
        txtPassword.Font = New Font("Segoe UI", 10)
        txtPassword.Size = New Size(280, 25)
        txtPassword.Location = New Point(10, yPos)
        txtPassword.BorderStyle = BorderStyle.FixedSingle
        txtPassword.UseSystemPasswordChar = True
        parent.Controls.Add(txtPassword)
        yPos += 35

        ' Nama Lengkap
        Dim lblNamaLengkap As New Label()
        lblNamaLengkap.Text = "Nama Lengkap:"
        lblNamaLengkap.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblNamaLengkap.ForeColor = Color.FromArgb(52, 73, 94)
        lblNamaLengkap.Size = New Size(120, 25)
        lblNamaLengkap.Location = New Point(10, yPos)
        parent.Controls.Add(lblNamaLengkap)
        yPos += 25

        Dim txtNamaLengkap As New TextBox()
        txtNamaLengkap.Name = "txtNamaLengkap"
        txtNamaLengkap.Font = New Font("Segoe UI", 10)
        txtNamaLengkap.Size = New Size(280, 25)
        txtNamaLengkap.Location = New Point(10, yPos)
        txtNamaLengkap.BorderStyle = BorderStyle.FixedSingle
        parent.Controls.Add(txtNamaLengkap)
        yPos += 35

        ' Email
        Dim lblEmail As New Label()
        lblEmail.Text = "Email:"
        lblEmail.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblEmail.ForeColor = Color.FromArgb(52, 73, 94)
        lblEmail.Size = New Size(120, 25)
        lblEmail.Location = New Point(10, yPos)
        parent.Controls.Add(lblEmail)
        yPos += 25

        Dim txtEmail As New TextBox()
        txtEmail.Name = "txtEmail"
        txtEmail.Font = New Font("Segoe UI", 10)
        txtEmail.Size = New Size(280, 25)
        txtEmail.Location = New Point(10, yPos)
        txtEmail.BorderStyle = BorderStyle.FixedSingle
        txtEmail.CharacterCasing = CharacterCasing.Lower
        parent.Controls.Add(txtEmail)
        yPos += 35

        ' Role
        Dim lblRole As New Label()
        lblRole.Text = "Role:"
        lblRole.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        lblRole.ForeColor = Color.FromArgb(52, 73, 94)
        lblRole.Size = New Size(120, 25)
        lblRole.Location = New Point(10, yPos)
        parent.Controls.Add(lblRole)
        yPos += 25

        Dim cmbRole As New ComboBox()
        cmbRole.Name = "cmbRole"
        cmbRole.Font = New Font("Segoe UI", 10)
        cmbRole.Size = New Size(280, 25)
        cmbRole.Location = New Point(10, yPos)
        cmbRole.DropDownStyle = ComboBoxStyle.DropDownList
        cmbRole.Items.AddRange({"kasir", "manager", "admin"})
        cmbRole.SelectedIndex = 0
        parent.Controls.Add(cmbRole)
        yPos += 50

        ' Note for edit mode
        Dim lblNote As New Label()
        lblNote.Name = "lblNote"
        lblNote.Text = "* Kosongkan password jika tidak ingin mengubah"
        lblNote.Font = New Font("Segoe UI", 8, FontStyle.Italic)
        lblNote.ForeColor = Color.FromArgb(149, 165, 166)
        lblNote.Size = New Size(280, 20)
        lblNote.Location = New Point(10, yPos)
        lblNote.Visible = False
        parent.Controls.Add(lblNote)
        yPos += 30

        ' Buttons Panel
        Dim buttonPanel As New Panel()
        buttonPanel.Size = New Size(280, 50)
        buttonPanel.Location = New Point(10, yPos)
        parent.Controls.Add(buttonPanel)

        ' Save Button
        Dim btnSave As New Button()
        btnSave.Name = "btnSimpan"
        btnSave.Text = "SIMPAN"
        btnSave.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        btnSave.Size = New Size(90, 35)
        btnSave.Location = New Point(0, 0)
        btnSave.BackColor = Color.FromArgb(46, 204, 113)
        btnSave.ForeColor = Color.White
        btnSave.FlatStyle = FlatStyle.Flat
        btnSave.FlatAppearance.BorderSize = 0
        btnSave.Cursor = Cursors.Hand
        AddHandler btnSave.Click, AddressOf BtnSimpan_Click
        buttonPanel.Controls.Add(btnSave)

        ' Update Button
        Dim btnUpdate As New Button()
        btnUpdate.Name = "btnUpdate"
        btnUpdate.Text = "UPDATE"
        btnUpdate.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        btnUpdate.Size = New Size(90, 35)
        btnUpdate.Location = New Point(95, 0)
        btnUpdate.BackColor = Color.FromArgb(52, 152, 219)
        btnUpdate.ForeColor = Color.White
        btnUpdate.FlatStyle = FlatStyle.Flat
        btnUpdate.FlatAppearance.BorderSize = 0
        btnUpdate.Cursor = Cursors.Hand
        AddHandler btnUpdate.Click, AddressOf BtnUpdate_Click
        buttonPanel.Controls.Add(btnUpdate)

        ' Cancel Button
        Dim btnCancel As New Button()
        btnCancel.Name = "btnBatal"
        btnCancel.Text = "BATAL"
        btnCancel.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        btnCancel.Size = New Size(90, 35)
        btnCancel.Location = New Point(190, 0)
        btnCancel.BackColor = Color.FromArgb(149, 165, 166)
        btnCancel.ForeColor = Color.White
        btnCancel.FlatStyle = FlatStyle.Flat
        btnCancel.FlatAppearance.BorderSize = 0
        btnCancel.Cursor = Cursors.Hand
        AddHandler btnCancel.Click, AddressOf BtnBatal_Click
        buttonPanel.Controls.Add(btnCancel)
    End Sub

    Private Sub CreateDataGridControls(parent As Panel)
        ' Title
        Dim lblTitle As New Label()
        lblTitle.Text = "DAFTAR USER"
        lblTitle.Font = New Font("Segoe UI", 14, FontStyle.Bold)
        lblTitle.ForeColor = Color.FromArgb(52, 73, 94)
        lblTitle.Size = New Size(300, 30)
        lblTitle.Location = New Point(10, 10)
        parent.Controls.Add(lblTitle)

        ' Search Panel
        Dim searchPanel As New Panel()
        searchPanel.Size = New Size(parent.Width - 30, 40)
        searchPanel.Location = New Point(10, 50)
        parent.Controls.Add(searchPanel)

        Dim lblSearch As New Label()
        lblSearch.Text = "Cari:"
        lblSearch.Font = New Font("Segoe UI", 10)
        lblSearch.Size = New Size(40, 25)
        lblSearch.Location = New Point(0, 8)
        searchPanel.Controls.Add(lblSearch)

        Dim txtSearch As New TextBox()
        txtSearch.Name = "txtCari"
        txtSearch.Font = New Font("Segoe UI", 10)
        txtSearch.Size = New Size(200, 25)
        txtSearch.Location = New Point(45, 5)
        txtSearch.BorderStyle = BorderStyle.FixedSingle
        AddHandler txtSearch.TextChanged, AddressOf TxtCari_TextChanged
        searchPanel.Controls.Add(txtSearch)

        Dim btnRefresh As New Button()
        btnRefresh.Name = "btnRefresh"
        btnRefresh.Text = "REFRESH"
        btnRefresh.Font = New Font("Segoe UI", 9, FontStyle.Bold)
        btnRefresh.Size = New Size(80, 25)
        btnRefresh.Location = New Point(250, 5)
        btnRefresh.BackColor = Color.FromArgb(52, 152, 219)
        btnRefresh.ForeColor = Color.White
        btnRefresh.FlatStyle = FlatStyle.Flat
        btnRefresh.FlatAppearance.BorderSize = 0
        btnRefresh.Cursor = Cursors.Hand
        AddHandler btnRefresh.Click, AddressOf BtnRefresh_Click
        searchPanel.Controls.Add(btnRefresh)

        ' DataGridView
        Dim dgv As New DataGridView()
        dgv.Name = "dgvUser"
        dgv.Size = New Size(parent.Width - 30, parent.Height - 140)
        dgv.Location = New Point(10, 100)
        dgv.Anchor = AnchorStyles.Top Or AnchorStyles.Bottom Or AnchorStyles.Left Or AnchorStyles.Right
        parent.Controls.Add(dgv)

        ' Action Buttons Panel
        Dim actionPanel As New Panel()
        actionPanel.Size = New Size(parent.Width - 30, 40)
        actionPanel.Location = New Point(10, parent.Height - 50)
        actionPanel.Anchor = AnchorStyles.Bottom Or AnchorStyles.Left Or AnchorStyles.Right
        parent.Controls.Add(actionPanel)

        Dim btnEdit As New Button()
        btnEdit.Name = "btnEdit"
        btnEdit.Text = "EDIT"
        btnEdit.Font = New Font("Segoe UI", 9, FontStyle.Bold)
        btnEdit.Size = New Size(70, 30)
        btnEdit.Location = New Point(0, 5)
        btnEdit.BackColor = Color.FromArgb(243, 156, 18)
        btnEdit.ForeColor = Color.White
        btnEdit.FlatStyle = FlatStyle.Flat
        btnEdit.FlatAppearance.BorderSize = 0
        btnEdit.Cursor = Cursors.Hand
        AddHandler btnEdit.Click, AddressOf BtnEdit_Click
        actionPanel.Controls.Add(btnEdit)

        Dim btnDelete As New Button()
        btnDelete.Name = "btnHapus"
        btnDelete.Text = "HAPUS"
        btnDelete.Font = New Font("Segoe UI", 9, FontStyle.Bold)
        btnDelete.Size = New Size(70, 30)
        btnDelete.Location = New Point(75, 5)
        btnDelete.BackColor = Color.FromArgb(231, 76, 60)
        btnDelete.ForeColor = Color.White
        btnDelete.FlatStyle = FlatStyle.Flat
        btnDelete.FlatAppearance.BorderSize = 0
        btnDelete.Cursor = Cursors.Hand
        AddHandler btnDelete.Click, AddressOf BtnHapus_Click
        actionPanel.Controls.Add(btnDelete)

        Dim btnResetPassword As New Button()
        btnResetPassword.Name = "btnResetPassword"
        btnResetPassword.Text = "RESET PWD"
        btnResetPassword.Font = New Font("Segoe UI", 9, FontStyle.Bold)
        btnResetPassword.Size = New Size(90, 30)
        btnResetPassword.Location = New Point(150, 5)
        btnResetPassword.BackColor = Color.FromArgb(155, 89, 182)
        btnResetPassword.ForeColor = Color.White
        btnResetPassword.FlatStyle = FlatStyle.Flat
        btnResetPassword.FlatAppearance.BorderSize = 0
        btnResetPassword.Cursor = Cursors.Hand
        AddHandler btnResetPassword.Click, AddressOf BtnResetPassword_Click
        actionPanel.Controls.Add(btnResetPassword)
    End Sub

    Private Sub SetupDataGridView()
        Dim dgv As DataGridView = DirectCast(Me.Controls.Find("dgvUser", True)(0), DataGridView)

        ' Set DataGridView properties
        dgv.AllowUserToAddRows = False
        dgv.AllowUserToDeleteRows = False
        dgv.ReadOnly = True
        dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        dgv.MultiSelect = False
        dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        dgv.BackgroundColor = Color.White
        dgv.BorderStyle = BorderStyle.None
        dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal
        dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219)
        dgv.DefaultCellStyle.SelectionForeColor = Color.White
        dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94)
        dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White
        dgv.ColumnHeadersDefaultCellStyle.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        dgv.EnableHeadersVisualStyles = False
        dgv.RowHeadersVisible = False

        ' Add double click event
        AddHandler dgv.CellDoubleClick, AddressOf DgvUser_CellDoubleClick
    End Sub

    Private Sub LoadUserData()
        Try
            Dim dgv As DataGridView = DirectCast(Me.Controls.Find("dgvUser", True)(0), DataGridView)
            Dim listUser As List(Of User) = userRepo.GetAllUser()

            ' Create DataTable
            Dim dt As New DataTable()
            dt.Columns.Add("ID", GetType(Integer))
            dt.Columns.Add("Username", GetType(String))
            dt.Columns.Add("Nama Lengkap", GetType(String))
            dt.Columns.Add("Email", GetType(String))
            dt.Columns.Add("Role", GetType(String))
            dt.Columns.Add("Tanggal Dibuat", GetType(DateTime))
            dt.Columns.Add("Login Terakhir", GetType(String))

            ' Fill DataTable
            For Each user In listUser
                Dim loginTerakhir As String = If(user.TanggalLoginTerakhir.HasValue, user.TanggalLoginTerakhir.Value.ToString("dd/MM/yyyy HH:mm"), "Belum pernah")
                dt.Rows.Add(user.IdUser, user.Username, user.NamaLengkap, user.Email, user.Role.ToUpper(), user.TanggalDibuat, loginTerakhir)
            Next

            dgv.DataSource = dt

            ' Hide ID column and format columns
            If dgv.Columns.Count > 0 Then
                dgv.Columns("ID").Visible = False
                dgv.Columns("Role").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter
                dgv.Columns("Tanggal Dibuat").DefaultCellStyle.Format = "dd/MM/yyyy"

                ' Color coding for roles
                For Each row As DataGridViewRow In dgv.Rows
                    Dim role As String = row.Cells("Role").Value.ToString().ToLower()
                    Select Case role
                        Case "admin"
                            row.Cells("Role").Style.BackColor = Color.FromArgb(231, 76, 60)
                            row.Cells("Role").Style.ForeColor = Color.White
                        Case "manager"
                            row.Cells("Role").Style.BackColor = Color.FromArgb(243, 156, 18)
                            row.Cells("Role").Style.ForeColor = Color.White
                        Case "kasir"
                            row.Cells("Role").Style.BackColor = Color.FromArgb(46, 204, 113)
                            row.Cells("Role").Style.ForeColor = Color.White
                    End Select
                Next
            End If

        Catch ex As Exception
            MessageBox.Show("Error loading data: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetFormMode(editMode As Boolean)
        isEditMode = editMode

        Dim btnSimpan As Button = DirectCast(Me.Controls.Find("btnSimpan", True)(0), Button)
        Dim btnUpdate As Button = DirectCast(Me.Controls.Find("btnUpdate", True)(0), Button)
        Dim lblNote As Label = DirectCast(Me.Controls.Find("lblNote", True)(0), Label)
        Dim txtPassword As TextBox = DirectCast(Me.Controls.Find("txtPassword", True)(0), TextBox)

        btnSimpan.Visible = Not editMode
        btnUpdate.Visible = editMode
        lblNote.Visible = editMode

        If editMode Then
            txtPassword.PlaceholderText = "Kosongkan jika tidak ingin mengubah"
        Else
            txtPassword.PlaceholderText = ""
        End If

        If Not editMode Then
            ClearForm()
            selectedUserId = 0
        End If
    End Sub

    Private Sub ClearForm()
        Dim txtUsername As TextBox = DirectCast(Me.Controls.Find("txtUsername", True)(0), TextBox)
        Dim txtPassword As TextBox = DirectCast(Me.Controls.Find("txtPassword", True)(0), TextBox)
        Dim txtNamaLengkap As TextBox = DirectCast(Me.Controls.Find("txtNamaLengkap", True)(0), TextBox)
        Dim txtEmail As TextBox = DirectCast(Me.Controls.Find("txtEmail", True)(0), TextBox)
        Dim cmbRole As ComboBox = DirectCast(Me.Controls.Find("cmbRole", True)(0), ComboBox)

        txtUsername.Clear()
        txtPassword.Clear()
        txtNamaLengkap.Clear()
        txtEmail.Clear()
        cmbRole.SelectedIndex = 0
        txtUsername.Focus()
    End Sub

    Private Sub BtnSimpan_Click(sender As Object, e As EventArgs)
        Try
            Dim user As New User()

            ' Get form values
            Dim txtUsername As TextBox = DirectCast(Me.Controls.Find("txtUsername", True)(0), TextBox)
            Dim txtPassword As TextBox = DirectCast(Me.Controls.Find("txtPassword", True)(0), TextBox)
            Dim txtNamaLengkap As TextBox = DirectCast(Me.Controls.Find("txtNamaLengkap", True)(0), TextBox)
            Dim txtEmail As TextBox = DirectCast(Me.Controls.Find("txtEmail", True)(0), TextBox)
            Dim cmbRole As ComboBox = DirectCast(Me.Controls.Find("cmbRole", True)(0), ComboBox)

            user.Username = txtUsername.Text.Trim()
            user.Password = txtPassword.Text
            user.NamaLengkap = txtNamaLengkap.Text.Trim()
            user.Email = txtEmail.Text.Trim()
            user.Role = cmbRole.SelectedItem.ToString()

            Dim result As DatabaseResponse = userRepo.TambahUser(user)

            If result.Success Then
                MessageBox.Show(result.Message, "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadUserData()
                ClearForm()
            Else
                MessageBox.Show(result.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If

        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnUpdate_Click(sender As Object, e As EventArgs)
        Try
            Dim user As New User()
            user.IdUser = selectedUserId

            ' Get form values
            Dim txtUsername As TextBox = DirectCast(Me.Controls.Find("txtUsername", True)(0), TextBox)
            Dim txtPassword As TextBox = DirectCast(Me.Controls.Find("txtPassword", True)(0), TextBox)
            Dim txtNamaLengkap As TextBox = DirectCast(Me.Controls.Find("txtNamaLengkap", True)(0), TextBox)
            Dim txtEmail As TextBox = DirectCast(Me.Controls.Find("txtEmail", True)(0), TextBox)
            Dim cmbRole As ComboBox = DirectCast(Me.Controls.Find("cmbRole", True)(0), ComboBox)

            user.Username = txtUsername.Text.Trim()
            user.NamaLengkap = txtNamaLengkap.Text.Trim()
            user.Email = txtEmail.Text.Trim()
            user.Role = cmbRole.SelectedItem.ToString()

            Dim result As DatabaseResponse = userRepo.UpdateUser(user)

            ' Update password if provided
            If Not String.IsNullOrWhiteSpace(txtPassword.Text) Then
                Dim passwordResult As DatabaseResponse = userRepo.ChangePassword(selectedUserId, "temp", txtPassword.Text)
                If Not passwordResult.Success Then
                    MessageBox.Show("User berhasil diupdate, tetapi gagal mengubah password: " & passwordResult.Message, "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                End If
            End If

            If result.Success Then
                MessageBox.Show(result.Message, "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadUserData()
                SetFormMode(False)
            Else
                MessageBox.Show(result.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If

        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnBatal_Click(sender As Object, e As EventArgs)
        SetFormMode(False)
    End Sub

    Private Sub BtnEdit_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = DirectCast(Me.Controls.Find("dgvUser", True)(0), DataGridView)

        If dgv.SelectedRows.Count > 0 Then
            Dim selectedRow As DataGridViewRow = dgv.SelectedRows(0)
            selectedUserId = Convert.ToInt32(selectedRow.Cells("ID").Value)

            ' Fill form with selected data
            Dim txtUsername As TextBox = DirectCast(Me.Controls.Find("txtUsername", True)(0), TextBox)
            Dim txtPassword As TextBox = DirectCast(Me.Controls.Find("txtPassword", True)(0), TextBox)
            Dim txtNamaLengkap As TextBox = DirectCast(Me.Controls.Find("txtNamaLengkap", True)(0), TextBox)
            Dim txtEmail As TextBox = DirectCast(Me.Controls.Find("txtEmail", True)(0), TextBox)
            Dim cmbRole As ComboBox = DirectCast(Me.Controls.Find("cmbRole", True)(0), ComboBox)

            txtUsername.Text = selectedRow.Cells("Username").Value.ToString()
            txtPassword.Clear() ' Don't show password
            txtNamaLengkap.Text = selectedRow.Cells("Nama Lengkap").Value.ToString()
            txtEmail.Text = If(IsDBNull(selectedRow.Cells("Email").Value), "", selectedRow.Cells("Email").Value.ToString())

            ' Set role
            Dim role As String = selectedRow.Cells("Role").Value.ToString().ToLower()
            For i As Integer = 0 To cmbRole.Items.Count - 1
                If cmbRole.Items(i).ToString() = role Then
                    cmbRole.SelectedIndex = i
                    Exit For
                End If
            Next

            SetFormMode(True)
        Else
            MessageBox.Show("Pilih user yang akan diedit!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub BtnHapus_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = DirectCast(Me.Controls.Find("dgvUser", True)(0), DataGridView)

        If dgv.SelectedRows.Count > 0 Then
            Dim selectedRow As DataGridViewRow = dgv.SelectedRows(0)
            Dim username As String = selectedRow.Cells("Username").Value.ToString()
            Dim userId As Integer = Convert.ToInt32(selectedRow.Cells("ID").Value)

            ' Prevent deleting current user
            If userId = CurrentUser.Instance.IdUser Then
                MessageBox.Show("Tidak dapat menghapus user yang sedang login!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            If MessageBox.Show($"Apakah Anda yakin ingin menghapus user '{username}'?", "Konfirmasi Hapus", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                Dim result As DatabaseResponse = userRepo.HapusUser(userId)

                If result.Success Then
                    MessageBox.Show(result.Message, "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    LoadUserData()
                    SetFormMode(False)
                Else
                    MessageBox.Show(result.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End If
            End If
        Else
            MessageBox.Show("Pilih user yang akan dihapus!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub BtnResetPassword_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = DirectCast(Me.Controls.Find("dgvUser", True)(0), DataGridView)

        If dgv.SelectedRows.Count > 0 Then
            Dim selectedRow As DataGridViewRow = dgv.SelectedRows(0)
            Dim username As String = selectedRow.Cells("Username").Value.ToString()
            Dim userId As Integer = Convert.ToInt32(selectedRow.Cells("ID").Value)

            If MessageBox.Show($"Apakah Anda yakin ingin mereset password user '{username}' menjadi 'password123'?", "Konfirmasi Reset Password", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                ' For demo purposes, we'll set a default password
                ' In production, you might want to generate a random password or send email
                Dim defaultPassword As String = "password123"

                ' We need to get current password first, but for reset we'll use a different approach
                ' This is a simplified version - in production you'd have a proper reset mechanism
                Try
                    ' Direct database update for password reset (simplified approach)
                    Dim query As String = "UPDATE tb_user SET password = @password WHERE id_user = @id"
                    Dim parameters As New Dictionary(Of String, Object) From {
                        {"@password", defaultPassword},
                        {"@id", userId}
                    }

                    If DatabaseHelper.ExecuteNonQuery(query, parameters) Then
                        MessageBox.Show($"Password user '{username}' berhasil direset menjadi '{defaultPassword}'", "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    Else
                        MessageBox.Show("Gagal mereset password!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    End If

                Catch ex As Exception
                    MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End Try
            End If
        Else
            MessageBox.Show("Pilih user yang akan direset passwordnya!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        LoadUserData()
        SetFormMode(False)
    End Sub

    Private Sub TxtCari_TextChanged(sender As Object, e As EventArgs)
        Dim txtCari As TextBox = DirectCast(sender, TextBox)
        Dim dgv As DataGridView = DirectCast(Me.Controls.Find("dgvUser", True)(0), DataGridView)

        If dgv.DataSource IsNot Nothing Then
            Dim dt As DataTable = DirectCast(dgv.DataSource, DataTable)
            If String.IsNullOrWhiteSpace(txtCari.Text) Then
                dt.DefaultView.RowFilter = ""
            Else
                dt.DefaultView.RowFilter = $"[Username] LIKE '%{txtCari.Text}%' OR [Nama Lengkap] LIKE '%{txtCari.Text}%' OR [Email] LIKE '%{txtCari.Text}%'"
            End If
        End If
    End Sub

    Private Sub DgvUser_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs)
        If e.RowIndex >= 0 Then
            BtnEdit_Click(Nothing, Nothing)
        End If
    End Sub
End Class
