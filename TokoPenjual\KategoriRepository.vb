﻿Imports MySql.Data.MySqlClient

Public Class KategoriRepository

    ' Mendapatkan semua kategori aktif
    Public Function GetAllKategori() As List(Of Kategori)
        Dim listKategori As New List(Of Kategori)
        Dim query As String = "SELECT id_kategori, nama_kategori, deskripsi, tanggal_dibuat, tanggal_diubah, status_aktif FROM tb_kategori WHERE status_aktif = 1 ORDER BY nama_kategori"

        Try
            Dim dt As DataTable = DatabaseHelper.ExecuteQuery(query)
            For Each row As DataRow In dt.Rows
                Dim kategori As New Kategori()
                kategori.IdKategori = Convert.ToInt32(row("id_kategori"))
                kategori.NamaKategori = row("nama_kategori").ToString()
                kategori.Deskripsi = If(IsDBNull(row("deskripsi")), "", row("deskripsi").ToString())
                kategori.TanggalDibuat = Convert.ToDateTime(row("tanggal_dibuat"))
                kategori.TanggalDiubah = Convert.ToDateTime(row("tanggal_diubah"))
                kategori.StatusAktif = Convert.ToBoolean(row("status_aktif"))
                listKategori.Add(kategori)
            Next
        Catch ex As Exception
            MessageBox.Show("Error saat mengambil data kategori: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Return listKategori
    End Function

    ' Mendapatkan kategori berdasarkan ID
    Public Function GetKategoriById(id As Integer) As Kategori
        Dim kategori As New Kategori()
        Dim query As String = "SELECT id_kategori, nama_kategori, deskripsi, tanggal_dibuat, tanggal_diubah, status_aktif FROM tb_kategori WHERE id_kategori = @id AND status_aktif = 1"
        Dim parameters As New Dictionary(Of String, Object) From {{"@id", id}}

        Try
            Dim dt As DataTable = DatabaseHelper.ExecuteQuery(query, parameters)
            If dt.Rows.Count > 0 Then
                Dim row As DataRow = dt.Rows(0)
                kategori.IdKategori = Convert.ToInt32(row("id_kategori"))
                kategori.NamaKategori = row("nama_kategori").ToString()
                kategori.Deskripsi = If(IsDBNull(row("deskripsi")), "", row("deskripsi").ToString())
                kategori.TanggalDibuat = Convert.ToDateTime(row("tanggal_dibuat"))
                kategori.TanggalDiubah = Convert.ToDateTime(row("tanggal_diubah"))
                kategori.StatusAktif = Convert.ToBoolean(row("status_aktif"))
            End If
        Catch ex As Exception
            MessageBox.Show("Error saat mengambil data kategori: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Return kategori
    End Function

    ' Menambah kategori baru
    Public Function TambahKategori(kategori As Kategori) As DatabaseResponse
        Dim response As New DatabaseResponse()

        ' Validasi input
        If String.IsNullOrWhiteSpace(kategori.NamaKategori) Then
            response.Message = "Nama kategori tidak boleh kosong!"
            Return response
        End If

        ' Cek apakah nama kategori sudah ada
        If IsKategoriExists(kategori.NamaKategori) Then
            response.Message = "Nama kategori sudah ada!"
            Return response
        End If

        Dim query As String = "INSERT INTO tb_kategori (nama_kategori, deskripsi) VALUES (@nama, @deskripsi)"
        Dim parameters As New Dictionary(Of String, Object) From {
            {"@nama", kategori.NamaKategori.Trim()},
            {"@deskripsi", If(String.IsNullOrWhiteSpace(kategori.Deskripsi), "", kategori.Deskripsi.Trim())}
        }

        Try
            If DatabaseHelper.ExecuteNonQuery(query, parameters) Then
                response.Success = True
                response.Message = "Kategori berhasil ditambahkan!"
                response.Data = DatabaseHelper.GetLastInsertId()
            Else
                response.Message = "Gagal menambahkan kategori!"
            End If
        Catch ex As Exception
            response.Message = "Error: " & ex.Message
        End Try

        Return response
    End Function

    ' Mengupdate kategori
    Public Function UpdateKategori(kategori As Kategori) As DatabaseResponse
        Dim response As New DatabaseResponse()

        ' Validasi input
        If kategori.IdKategori <= 0 Then
            response.Message = "ID kategori tidak valid!"
            Return response
        End If

        If String.IsNullOrWhiteSpace(kategori.NamaKategori) Then
            response.Message = "Nama kategori tidak boleh kosong!"
            Return response
        End If

        ' Cek apakah nama kategori sudah ada (kecuali untuk kategori yang sedang diedit)
        If IsKategoriExists(kategori.NamaKategori, kategori.IdKategori) Then
            response.Message = "Nama kategori sudah ada!"
            Return response
        End If

        Dim query As String = "UPDATE tb_kategori SET nama_kategori = @nama, deskripsi = @deskripsi WHERE id_kategori = @id"
        Dim parameters As New Dictionary(Of String, Object) From {
            {"@nama", kategori.NamaKategori.Trim()},
            {"@deskripsi", If(String.IsNullOrWhiteSpace(kategori.Deskripsi), "", kategori.Deskripsi.Trim())},
            {"@id", kategori.IdKategori}
        }

        Try
            If DatabaseHelper.ExecuteNonQuery(query, parameters) Then
                response.Success = True
                response.Message = "Kategori berhasil diupdate!"
            Else
                response.Message = "Gagal mengupdate kategori!"
            End If
        Catch ex As Exception
            response.Message = "Error: " & ex.Message
        End Try

        Return response
    End Function

    ' Menghapus kategori (soft delete)
    Public Function HapusKategori(id As Integer) As DatabaseResponse
        Dim response As New DatabaseResponse()

        ' Validasi input
        If id <= 0 Then
            response.Message = "ID kategori tidak valid!"
            Return response
        End If

        ' Cek apakah kategori masih digunakan oleh produk
        If IsKategoriUsedByProduk(id) Then
            response.Message = "Kategori tidak dapat dihapus karena masih digunakan oleh produk!"
            Return response
        End If

        Dim query As String = "UPDATE tb_kategori SET status_aktif = 0 WHERE id_kategori = @id"
        Dim parameters As New Dictionary(Of String, Object) From {{"@id", id}}

        Try
            If DatabaseHelper.ExecuteNonQuery(query, parameters) Then
                response.Success = True
                response.Message = "Kategori berhasil dihapus!"
            Else
                response.Message = "Gagal menghapus kategori!"
            End If
        Catch ex As Exception
            response.Message = "Error: " & ex.Message
        End Try

        Return response
    End Function

    ' Cek apakah nama kategori sudah ada
    Private Function IsKategoriExists(namaKategori As String, Optional excludeId As Integer = 0) As Boolean
        Dim query As String = "SELECT COUNT(*) FROM tb_kategori WHERE nama_kategori = @nama AND status_aktif = 1"
        Dim parameters As New Dictionary(Of String, Object) From {{"@nama", namaKategori.Trim()}}

        If excludeId > 0 Then
            query &= " AND id_kategori != @excludeId"
            parameters.Add("@excludeId", excludeId)
        End If

        Try
            Dim count As Integer = Convert.ToInt32(DatabaseHelper.ExecuteScalar(query, parameters))
            Return count > 0
        Catch ex As Exception
            Return False
        End Try
    End Function

    ' Cek apakah kategori masih digunakan oleh produk
    Private Function IsKategoriUsedByProduk(idKategori As Integer) As Boolean
        Dim query As String = "SELECT COUNT(*) FROM tb_produk WHERE id_kategori = @id AND status_aktif = 1"
        Dim parameters As New Dictionary(Of String, Object) From {{"@id", idKategori}}

        Try
            Dim count As Integer = Convert.ToInt32(DatabaseHelper.ExecuteScalar(query, parameters))
            Return count > 0
        Catch ex As Exception
            Return False
        End Try
    End Function

    ' Mendapatkan total kategori
    Public Function GetTotalKategori() As Integer
        Dim query As String = "SELECT COUNT(*) FROM tb_kategori WHERE status_aktif = 1"
        Try
            Return Convert.ToInt32(DatabaseHelper.ExecuteScalar(query))
        Catch ex As Exception
            Return 0
        End Try
    End Function


End Class
