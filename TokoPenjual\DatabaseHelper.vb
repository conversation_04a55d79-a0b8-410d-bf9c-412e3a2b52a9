﻿Imports MySql.Data.MySqlClient

Public Class DatabaseHelper
    ' Fungsi untuk mengeksekusi query SELECT dan mengembalikan DataTable
    Public Shared Function ExecuteQuery(query As String, Optional parameters As Dictionary(Of String, Object) = Nothing) As DataTable
        Dim dt As New DataTable()
        Try
            ModuleKoneksi.koneksi()
            ModuleKoneksi.cmd = New MySqlCommand(query, ModuleKoneksi.conn)

            ' Tambahkan parameter jika ada
            If parameters IsNot Nothing Then
                For Each param In parameters
                    ModuleKoneksi.cmd.Parameters.AddWithValue(param.Key, param.Value)
                Next
            End If

            ModuleKoneksi.da = New MySqlDataAdapter(ModuleKoneksi.cmd)
            ModuleKoneksi.da.Fill(dt)

        Catch ex As Exception
            MessageBox.Show("Error saat mengeksekusi query: " & ex.Message, "Error Database", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            ModuleKoneksi.tutupKoneksi()
        End Try
        Return dt
    End Function

    ' Fungsi untuk mengeksekusi query INSERT, UPDATE, DELETE
    Public Shared Function ExecuteNonQuery(query As String, Optional parameters As Dictionary(Of String, Object) = Nothing) As Boolean
        Try
            ModuleKoneksi.koneksi()
            ModuleKoneksi.cmd = New MySqlCommand(query, ModuleKoneksi.conn)

            ' Tambahkan parameter jika ada
            If parameters IsNot Nothing Then
                For Each param In parameters
                    ModuleKoneksi.cmd.Parameters.AddWithValue(param.Key, param.Value)
                Next
            End If

            Dim result As Integer = ModuleKoneksi.cmd.ExecuteNonQuery()
            Return result > 0

        Catch ex As Exception
            MessageBox.Show("Error saat mengeksekusi perintah: " & ex.Message, "Error Database", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        Finally
            ModuleKoneksi.tutupKoneksi()
        End Try
    End Function

    ' Fungsi untuk mendapatkan nilai scalar (seperti COUNT, MAX, dll)
    Public Shared Function ExecuteScalar(query As String, Optional parameters As Dictionary(Of String, Object) = Nothing) As Object
        Try
            ModuleKoneksi.koneksi()
            ModuleKoneksi.cmd = New MySqlCommand(query, ModuleKoneksi.conn)

            ' Tambahkan parameter jika ada
            If parameters IsNot Nothing Then
                For Each param In parameters
                    ModuleKoneksi.cmd.Parameters.AddWithValue(param.Key, param.Value)
                Next
            End If

            Return ModuleKoneksi.cmd.ExecuteScalar()

        Catch ex As Exception
            MessageBox.Show("Error saat mengeksekusi scalar: " & ex.Message, "Error Database", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return Nothing
        Finally
            ModuleKoneksi.tutupKoneksi()
        End Try
    End Function

    ' Fungsi untuk validasi koneksi database
    Public Shared Function TestConnection() As Boolean
        Try
            ModuleKoneksi.koneksi()
            Return ModuleKoneksi.conn.State = ConnectionState.Open
        Catch ex As Exception
            MessageBox.Show("Koneksi database gagal: " & ex.Message, "Error Koneksi", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        Finally
            ModuleKoneksi.tutupKoneksi()
        End Try
    End Function

    ' Fungsi untuk mendapatkan ID terakhir yang di-insert
    Public Shared Function GetLastInsertId() As Integer
        Try
            ModuleKoneksi.koneksi()
            ModuleKoneksi.cmd = New MySqlCommand("SELECT LAST_INSERT_ID()", ModuleKoneksi.conn)
            Dim result = ModuleKoneksi.cmd.ExecuteScalar()
            Return Convert.ToInt32(result)
        Catch ex As Exception
            MessageBox.Show("Error mendapatkan ID terakhir: " & ex.Message, "Error Database", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return 0
        Finally
            ModuleKoneksi.tutupKoneksi()
        End Try
    End Function
End Class
